# -*- coding: utf-8 -*-
"""
ملف إعداد قاعدة البيانات لنظام إدارة المبيعات والمخزون
يحتوي على جميع الجداول والعلاقات المطلوبة
"""

import mysql.connector
from mysql.connector import Error
import logging
from datetime import datetime

# إعداد السجلات
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class قاعدة_البيانات:
    """
    كلاس إدارة قاعدة البيانات الرئيسي
    يحتوي على جميع العمليات المتعلقة بقاعدة البيانات
    """
    
    def __init__(self):
        """
        دالة التهيئة لإعداد الاتصال بقاعدة البيانات
        """
        self.host = "localhost"
        self.user = "root"
        self.password = "kh123456"
        self.database = "Sales_system"
        self.connection = None
        self.cursor = None
    
    def اتصال_قاعدة_البيانات(self):
        """
        دالة الاتصال بقاعدة البيانات MySQL
        """
        try:
            self.connection = mysql.connector.connect(
                host=self.host,
                user=self.user,
                password=self.password,
                charset='utf8mb4',
                collation='utf8mb4_unicode_ci'
            )
            self.cursor = self.connection.cursor()
            logger.info("تم الاتصال بقاعدة البيانات بنجاح")
            return True
        except Error as e:
            logger.error(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            return False
    
    def انشاء_قاعدة_البيانات(self):
        """
        دالة إنشاء قاعدة البيانات الرئيسية
        """
        try:
            if self.اتصال_قاعدة_البيانات():
                # إنشاء قاعدة البيانات
                self.cursor.execute(f"CREATE DATABASE IF NOT EXISTS {self.database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
                self.cursor.execute(f"USE {self.database}")
                self.connection.commit()
                logger.info(f"تم إنشاء قاعدة البيانات {self.database} بنجاح")
                return True
        except Error as e:
            logger.error(f"خطأ في إنشاء قاعدة البيانات: {e}")
            return False
    
    def انشاء_الجداول(self):
        """
        دالة إنشاء جميع الجداول المطلوبة
        """
        try:
            if not self.انشاء_قاعدة_البيانات():
                return False
            
            # جدول الفئات الرئيسية للمنتجات
            جدول_الفئات = """
            CREATE TABLE IF NOT EXISTS الفئات (
                رقم_الفئة INT AUTO_INCREMENT PRIMARY KEY,
                اسم_الفئة VARCHAR(100) NOT NULL UNIQUE,
                وصف_الفئة TEXT,
                فئة_رئيسية INT DEFAULT NULL,
                حالة_النشاط BOOLEAN DEFAULT TRUE,
                تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                تاريخ_التحديث TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (فئة_رئيسية) REFERENCES الفئات(رقم_الفئة) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """
            
            # جدول الموردين
            جدول_الموردين = """
            CREATE TABLE IF NOT EXISTS الموردين (
                رقم_المورد INT AUTO_INCREMENT PRIMARY KEY,
                اسم_المورد VARCHAR(100) NOT NULL,
                العنوان TEXT,
                رقم_الهاتف VARCHAR(20),
                البريد_الإلكتروني VARCHAR(100),
                حالة_النشاط ENUM('نشط', 'غير نشط') DEFAULT 'نشط',
                الرصيد_الحالي DECIMAL(15,2) DEFAULT 0.00,
                تاريخ_الإضافة TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                تاريخ_التحديث TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """
            
            # جدول المنتجات
            جدول_المنتجات = """
            CREATE TABLE IF NOT EXISTS المنتجات (
                رقم_المنتج INT AUTO_INCREMENT PRIMARY KEY,
                الباركود VARCHAR(50) UNIQUE NOT NULL,
                الاسم_العام VARCHAR(150) NOT NULL,
                الاسم_التجاري VARCHAR(150),
                رقم_الفئة INT,
                الوحدة VARCHAR(20) DEFAULT 'قطعة',
                سعر_الشراء DECIMAL(10,2) DEFAULT 0.00,
                سعر_البيع DECIMAL(10,2) NOT NULL,
                سعر_البيع_أقساط DECIMAL(10,2),
                الكمية_الحالية INT DEFAULT 0,
                الحد_الأدنى INT DEFAULT 0,
                يخضع_للصلاحية BOOLEAN DEFAULT FALSE,
                تاريخ_الصلاحية DATE NULL,
                حالة_النشاط BOOLEAN DEFAULT TRUE,
                تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                تاريخ_التحديث TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (رقم_الفئة) REFERENCES الفئات(رقم_الفئة) ON DELETE SET NULL,
                INDEX idx_باركود (الباركود),
                INDEX idx_اسم_عام (الاسم_العام),
                INDEX idx_اسم_تجاري (الاسم_التجاري)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """
            
            # جدول البنوك الرئيسية
            جدول_البنوك = """
            CREATE TABLE IF NOT EXISTS البنوك (
                رقم_البنك INT AUTO_INCREMENT PRIMARY KEY,
                اسم_البنك VARCHAR(100) NOT NULL,
                بنك_رئيسي INT DEFAULT NULL,
                رقم_حساب_الشركة VARCHAR(50),
                رقم_الهاتف VARCHAR(20),
                العنوان TEXT,
                حالة_النشاط BOOLEAN DEFAULT TRUE,
                تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (بنك_رئيسي) REFERENCES البنوك(رقم_البنك) ON DELETE SET NULL,
                INDEX idx_اسم_بنك (اسم_البنك),
                INDEX idx_هاتف_بنك (رقم_الهاتف)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """
            
            # جدول العملاء
            جدول_العملاء = """
            CREATE TABLE IF NOT EXISTS العملاء (
                رقم_العميل INT AUTO_INCREMENT PRIMARY KEY,
                الاسم_الأول VARCHAR(50) NOT NULL,
                اللقب VARCHAR(50) NOT NULL,
                رقم_الهاتف VARCHAR(20),
                العنوان TEXT,
                إثبات_شخصي VARCHAR(50),
                رقم_البنك INT,
                رقم_الحساب VARCHAR(50),
                الوكيل VARCHAR(100),
                من_طرف VARCHAR(100),
                رقم_العقد VARCHAR(50),
                الرصيد_الحالي DECIMAL(15,2) DEFAULT 0.00,
                حالة_النشاط BOOLEAN DEFAULT TRUE,
                تاريخ_التسجيل TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                تاريخ_التحديث TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (رقم_البنك) REFERENCES البنوك(رقم_البنك) ON DELETE SET NULL,
                INDEX idx_اسم_كامل (الاسم_الأول, اللقب),
                INDEX idx_هاتف (رقم_الهاتف)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """
            
            # جدول الموظفين
            جدول_الموظفين = """
            CREATE TABLE IF NOT EXISTS الموظفين (
                رقم_الموظف INT AUTO_INCREMENT PRIMARY KEY,
                الاسم_الكامل VARCHAR(100) NOT NULL,
                المنصب VARCHAR(50),
                رقم_الهاتف VARCHAR(20),
                العنوان TEXT,
                الراتب_الأساسي DECIMAL(10,2) DEFAULT 0.00,
                النسبة DECIMAL(5,2) DEFAULT 0.00,
                الرصيد_الحالي DECIMAL(15,2) DEFAULT 0.00,
                اسم_المستخدم VARCHAR(50) UNIQUE,
                كلمة_المرور VARCHAR(255),
                الصلاحيات TEXT,
                حالة_النشاط BOOLEAN DEFAULT TRUE,
                تاريخ_التوظيف DATE,
                تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_اسم_مستخدم (اسم_المستخدم)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """
            
            # تنفيذ إنشاء الجداول
            الجداول = [
                جدول_الفئات,
                جدول_الموردين,
                جدول_المنتجات,
                جدول_البنوك,
                جدول_العملاء,
                جدول_الموظفين
            ]
            
            for جدول in الجداول:
                self.cursor.execute(جدول)
                self.connection.commit()
            
            # جدول فواتير الشراء
            جدول_فواتير_الشراء = """
            CREATE TABLE IF NOT EXISTS فواتير_الشراء (
                رقم_الفاتورة INT AUTO_INCREMENT PRIMARY KEY,
                رقم_المورد INT NOT NULL,
                رقم_فاتورة_المورد VARCHAR(50),
                تاريخ_الشراء DATE NOT NULL,
                إجمالي_الفاتورة DECIMAL(15,2) DEFAULT 0.00,
                المبلغ_المدفوع DECIMAL(15,2) DEFAULT 0.00,
                المبلغ_المتبقي DECIMAL(15,2) DEFAULT 0.00,
                حالة_السداد ENUM('مسددة', 'جزئية', 'غير مسددة') DEFAULT 'غير مسددة',
                ملاحظات TEXT,
                تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (رقم_المورد) REFERENCES الموردين(رقم_المورد) ON DELETE CASCADE,
                INDEX idx_تاريخ_شراء (تاريخ_الشراء),
                INDEX idx_مورد (رقم_المورد)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """

            # جدول تفاصيل فواتير الشراء
            جدول_تفاصيل_فواتير_الشراء = """
            CREATE TABLE IF NOT EXISTS تفاصيل_فواتير_الشراء (
                رقم_التفصيل INT AUTO_INCREMENT PRIMARY KEY,
                رقم_الفاتورة INT NOT NULL,
                رقم_المنتج INT NOT NULL,
                الكمية INT NOT NULL,
                سعر_الشراء DECIMAL(10,2) NOT NULL,
                الإجمالي DECIMAL(15,2) NOT NULL,
                تاريخ_الصلاحية DATE NULL,
                FOREIGN KEY (رقم_الفاتورة) REFERENCES فواتير_الشراء(رقم_الفاتورة) ON DELETE CASCADE,
                FOREIGN KEY (رقم_المنتج) REFERENCES المنتجات(رقم_المنتج) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """

            # جدول فواتير البيع
            جدول_فواتير_البيع = """
            CREATE TABLE IF NOT EXISTS فواتير_البيع (
                رقم_الفاتورة INT AUTO_INCREMENT PRIMARY KEY,
                رقم_العميل INT NULL,
                نوع_البيع ENUM('نقدي', 'آجل', 'أقساط') DEFAULT 'نقدي',
                إجمالي_الفاتورة DECIMAL(15,2) NOT NULL,
                الخصم DECIMAL(15,2) DEFAULT 0.00,
                المبلغ_النهائي DECIMAL(15,2) NOT NULL,
                المبلغ_المدفوع DECIMAL(15,2) DEFAULT 0.00,
                المبلغ_المتبقي DECIMAL(15,2) DEFAULT 0.00,
                تاريخ_الفاتورة DATE NOT NULL,
                تاريخ_الاستحقاق DATE NULL,
                حالة_السداد ENUM('مسددة', 'جزئية', 'غير مسددة') DEFAULT 'مسددة',
                رقم_الموظف INT,
                ملاحظات TEXT,
                تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (رقم_العميل) REFERENCES العملاء(رقم_العميل) ON DELETE SET NULL,
                FOREIGN KEY (رقم_الموظف) REFERENCES الموظفين(رقم_الموظف) ON DELETE SET NULL,
                INDEX idx_تاريخ_فاتورة (تاريخ_الفاتورة),
                INDEX idx_عميل (رقم_العميل),
                INDEX idx_نوع_بيع (نوع_البيع)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """

            # جدول تفاصيل فواتير البيع
            جدول_تفاصيل_فواتير_البيع = """
            CREATE TABLE IF NOT EXISTS تفاصيل_فواتير_البيع (
                رقم_التفصيل INT AUTO_INCREMENT PRIMARY KEY,
                رقم_الفاتورة INT NOT NULL,
                رقم_المنتج INT NOT NULL,
                الكمية INT NOT NULL,
                سعر_البيع DECIMAL(10,2) NOT NULL,
                الإجمالي DECIMAL(15,2) NOT NULL,
                FOREIGN KEY (رقم_الفاتورة) REFERENCES فواتير_البيع(رقم_الفاتورة) ON DELETE CASCADE,
                FOREIGN KEY (رقم_المنتج) REFERENCES المنتجات(رقم_المنتج) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """

            # جدول العقود
            جدول_العقود = """
            CREATE TABLE IF NOT EXISTS العقود (
                رقم_العقد INT AUTO_INCREMENT PRIMARY KEY,
                رقم_العميل INT NOT NULL,
                رقم_الفاتورة INT NOT NULL,
                رقم_البنك INT NOT NULL,
                مبلغ_العقد DECIMAL(15,2) NOT NULL,
                القسط_الشهري DECIMAL(15,2) NOT NULL,
                عدد_الأقساط INT NOT NULL,
                تاريخ_بداية_العقد DATE NOT NULL,
                تاريخ_انتهاء_العقد DATE NOT NULL,
                حالة_العقد ENUM('نشط', 'مكتمل', 'ملغي') DEFAULT 'نشط',
                ملاحظات TEXT,
                تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (رقم_العميل) REFERENCES العملاء(رقم_العميل) ON DELETE CASCADE,
                FOREIGN KEY (رقم_الفاتورة) REFERENCES فواتير_البيع(رقم_الفاتورة) ON DELETE CASCADE,
                FOREIGN KEY (رقم_البنك) REFERENCES البنوك(رقم_البنك) ON DELETE RESTRICT
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """

            # جدول الأقساط
            جدول_الأقساط = """
            CREATE TABLE IF NOT EXISTS الأقساط (
                رقم_القسط INT AUTO_INCREMENT PRIMARY KEY,
                رقم_العقد INT NOT NULL,
                رقم_القسط_في_العقد INT NOT NULL,
                مبلغ_القسط DECIMAL(15,2) NOT NULL,
                تاريخ_الاستحقاق DATE NOT NULL,
                تاريخ_السداد DATE NULL,
                المبلغ_المدفوع DECIMAL(15,2) DEFAULT 0.00,
                حالة_السداد ENUM('مسدد', 'متأخر', 'غير مسدد') DEFAULT 'غير مسدد',
                ملاحظات TEXT,
                تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (رقم_العقد) REFERENCES العقود(رقم_العقد) ON DELETE CASCADE,
                INDEX idx_استحقاق (تاريخ_الاستحقاق),
                INDEX idx_عقد (رقم_العقد)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """

            # جدول المصروفات
            جدول_المصروفات = """
            CREATE TABLE IF NOT EXISTS المصروفات (
                رقم_المصروف INT AUTO_INCREMENT PRIMARY KEY,
                نوع_المصروف VARCHAR(100) NOT NULL,
                الوصف TEXT,
                المبلغ DECIMAL(15,2) NOT NULL,
                تاريخ_المصروف DATE NOT NULL,
                رقم_الموظف INT,
                ملاحظات TEXT,
                تاريخ_الإنشاء TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (رقم_الموظف) REFERENCES الموظفين(رقم_الموظف) ON DELETE SET NULL,
                INDEX idx_تاريخ_مصروف (تاريخ_المصروف),
                INDEX idx_نوع_مصروف (نوع_المصروف)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """

            # إضافة الجداول الجديدة للقائمة
            الجداول.extend([
                جدول_فواتير_الشراء,
                جدول_تفاصيل_فواتير_الشراء,
                جدول_فواتير_البيع,
                جدول_تفاصيل_فواتير_البيع,
                جدول_العقود,
                جدول_الأقساط,
                جدول_المصروفات
            ])

            for جدول in الجداول:
                self.cursor.execute(جدول)
                self.connection.commit()

            # تحديث جدول البنوك لإضافة الحقول الجديدة إذا لم تكن موجودة
            self.تحديث_جدول_البنوك()

            logger.info("تم إنشاء جميع الجداول بنجاح")
            return True

        except Error as e:
            logger.error(f"خطأ في إنشاء الجداول: {e}")
            return False

    def تحديث_جدول_البنوك(self):
        """
        تحديث جدول البنوك لإضافة الحقول الجديدة
        """
        try:
            # التحقق من وجود عمود رقم_الهاتف
            self.cursor.execute("""
                SELECT COUNT(*)
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_SCHEMA = %s
                AND TABLE_NAME = 'البنوك'
                AND COLUMN_NAME = 'رقم_الهاتف'
            """, (self.database,))

            if self.cursor.fetchone()[0] == 0:
                # إضافة عمود رقم الهاتف
                self.cursor.execute("""
                    ALTER TABLE البنوك
                    ADD COLUMN رقم_الهاتف VARCHAR(20) AFTER رقم_حساب_الشركة
                """)
                logger.info("تم إضافة عمود رقم_الهاتف لجدول البنوك")

            # التحقق من وجود عمود العنوان
            self.cursor.execute("""
                SELECT COUNT(*)
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_SCHEMA = %s
                AND TABLE_NAME = 'البنوك'
                AND COLUMN_NAME = 'العنوان'
            """, (self.database,))

            if self.cursor.fetchone()[0] == 0:
                # إضافة عمود العنوان
                self.cursor.execute("""
                    ALTER TABLE البنوك
                    ADD COLUMN العنوان TEXT AFTER رقم_الهاتف
                """)
                logger.info("تم إضافة عمود العنوان لجدول البنوك")

            # إضافة الفهارس إذا لم تكن موجودة
            try:
                self.cursor.execute("CREATE INDEX idx_هاتف_بنك ON البنوك(رقم_الهاتف)")
                logger.info("تم إضافة فهرس رقم الهاتف")
            except:
                pass  # الفهرس موجود بالفعل

            self.connection.commit()

        except Error as e:
            logger.error(f"خطأ في تحديث جدول البنوك: {e}")
            # لا نرجع False هنا لأن هذا تحديث اختياري

    def إدراج_بيانات_أولية(self):
        """
        دالة إدراج البيانات الأولية في الجداول
        """
        try:
            # إدراج فئات أولية للمنتجات
            فئات_أولية = [
                ('أدوية', 'الأدوية والمستحضرات الطبية'),
                ('مستحضرات تجميل', 'منتجات العناية والتجميل'),
                ('أغذية', 'المواد الغذائية والمشروبات'),
                ('منظفات', 'مواد التنظيف والتطهير'),
                ('أدوات منزلية', 'الأدوات والمعدات المنزلية')
            ]

            استعلام_فئات = "INSERT IGNORE INTO الفئات (اسم_الفئة, وصف_الفئة) VALUES (%s, %s)"
            self.cursor.executemany(استعلام_فئات, فئات_أولية)

            

            # إدراج مستخدم إداري افتراضي
            مستخدم_افتراضي = (
                'المدير العام',
                'مدير',
                '0500000000',
                'العنوان الافتراضي',
                10000.00,
                0.00,
                0.00,
                'admin',
                'admin123',  # يجب تشفيرها في التطبيق الحقيقي
                'جميع الصلاحيات',
                True,
                datetime.now().date()
            )

            استعلام_موظف = """
            INSERT IGNORE INTO الموظفين
            (الاسم_الكامل, المنصب, رقم_الهاتف, العنوان, الراتب_الأساسي,
             النسبة, الرصيد_الحالي, اسم_المستخدم, كلمة_المرور, الصلاحيات,
             حالة_النشاط, تاريخ_التوظيف)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            self.cursor.execute(استعلام_موظف, مستخدم_افتراضي)

            self.connection.commit()
            logger.info("تم إدراج البيانات الأولية بنجاح")
            return True

        except Error as e:
            logger.error(f"خطأ في إدراج البيانات الأولية: {e}")
            return False

    def تنفيذ_استعلام(self, استعلام, معاملات=None):
        """
        دالة تنفيذ استعلام SQL عام
        """
        try:
            if معاملات:
                self.cursor.execute(استعلام, معاملات)
            else:
                self.cursor.execute(استعلام)

            if استعلام.strip().upper().startswith('SELECT'):
                return self.cursor.fetchall()
            else:
                self.connection.commit()
                return self.cursor.rowcount

        except Error as e:
            logger.error(f"خطأ في تنفيذ الاستعلام: {e}")
            self.connection.rollback()
            return None

    def إغلاق_الاتصال(self):
        """
        دالة إغلاق الاتصال بقاعدة البيانات
        """
        try:
            if self.cursor:
                self.cursor.close()
            if self.connection:
                self.connection.close()
            logger.info("تم إغلاق الاتصال بقاعدة البيانات")
        except Error as e:
            logger.error(f"خطأ في إغلاق الاتصال: {e}")

def تهيئة_قاعدة_البيانات():
    """
    دالة تهيئة قاعدة البيانات الكاملة
    """
    قاعدة = قاعدة_البيانات()

    if قاعدة.انشاء_الجداول():
        if قاعدة.إدراج_بيانات_أولية():
            logger.info("تم تهيئة قاعدة البيانات بنجاح")
            قاعدة.إغلاق_الاتصال()
            return True

    قاعدة.إغلاق_الاتصال()
    return False

# تشغيل التهيئة عند استيراد الملف
if __name__ == "__main__":
    تهيئة_قاعدة_البيانات()
