# -*- coding: utf-8 -*-
"""
تطبيق إدارة المبيعات والمخزون الشامل
يدعم العربية بالكامل مع تخطيط RTL
"""

import sys
import os
from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *
from db import قاعدة_البيانات, تهيئة_قاعدة_البيانات
from pos_interface import واجهة_نقطة_البيع
from customers_interface import واجهة_إدارة_العملاء
from inventory_interface import واجهة_إدارة_المخزون
from suppliers_interface import واجهة_إدارة_الموردين
from other_interfaces import (واجهة_الموظفين, واجهة_المصروفات, واجهة_البنوك,
                             واجهة_العقود, واجهة_الأقساط, واجهة_التقارير_المالية, واجهة_الإعدادات)
from styles import get_main_stylesheet, get_responsive_styles, COLORS

class النافذة_الرئيسية(QMainWindow):
    """
    النافذة الرئيسية للتطبيق
    تحتوي على الشريط العلوي والقائمة الجانبية والمحتوى الرئيسي
    """
    
    def __init__(self):
        """
        دالة التهيئة للنافذة الرئيسية
        """
        super().__init__()
        self.قاعدة_البيانات = قاعدة_البيانات()
        self.إعداد_النافذة()
        self.إنشاء_الشريط_العلوي()
        self.إنشاء_القائمة_الجانبية()
        self.إنشاء_المحتوى_الرئيسي()
        self.تطبيق_الأنماط()
        
    def إعداد_النافذة(self):
        """
        دالة إعداد النافذة الرئيسية وخصائصها
        """
        self.setWindowTitle("نظام إدارة المبيعات والمخزون")
        self.setMinimumSize(1280, 720)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تعيين أيقونة التطبيق
        self.setWindowIcon(QIcon("icons/app_icon.png"))
        
        # إعداد النافذة المركزية
        self.النافذة_المركزية = QWidget()
        self.setCentralWidget(self.النافذة_المركزية)
        
        # التخطيط الرئيسي
        self.التخطيط_الرئيسي = QHBoxLayout(self.النافذة_المركزية)
        self.التخطيط_الرئيسي.setContentsMargins(0, 0, 0, 0)
        self.التخطيط_الرئيسي.setSpacing(0)
    
    def إنشاء_الشريط_العلوي(self):
        """
        دالة إنشاء الشريط العلوي مع القوائم
        """
        # إنشاء شريط القوائم
        شريط_القوائم = self.menuBar()
        شريط_القوائم.setLayoutDirection(Qt.RightToLeft)
        
        # قائمة ملف
        قائمة_ملف = شريط_القوائم.addMenu("ملف")
        قائمة_ملف.addAction("جديد", self.ملف_جديد, QKeySequence.New)
        قائمة_ملف.addAction("فتح", self.ملف_فتح, QKeySequence.Open)
        قائمة_ملف.addAction("حفظ", self.ملف_حفظ, QKeySequence.Save)
        قائمة_ملف.addSeparator()
        قائمة_ملف.addAction("خروج", self.close, QKeySequence.Quit)
        
        # قائمة معلومات
        قائمة_معلومات = شريط_القوائم.addMenu("معلومات")
        قائمة_معلومات.addAction("حول البرنامج", self.حول_البرنامج)
        قائمة_معلومات.addAction("معلومات النظام", self.معلومات_النظام)
        
        # قائمة حماية
        قائمة_حماية = شريط_القوائم.addMenu("حماية")
        قائمة_حماية.addAction("تسجيل دخول", self.تسجيل_دخول)
        قائمة_حماية.addAction("تسجيل خروج", self.تسجيل_خروج)
        قائمة_حماية.addAction("تغيير كلمة المرور", self.تغيير_كلمة_المرور)
        
        # قائمة اختصارات
        قائمة_اختصارات = شريط_القوائم.addMenu("اختصارات")
        قائمة_اختصارات.addAction("نقطة بيع", lambda: self.تغيير_الصفحة("نقطة بيع"), QKeySequence("F1"))
        قائمة_اختصارات.addAction("العملاء", lambda: self.تغيير_الصفحة("العملاء"), QKeySequence("F2"))
        قائمة_اختصارات.addAction("المخزن", lambda: self.تغيير_الصفحة("المخزن"), QKeySequence("F3"))
        
        # قائمة مساعدة
        قائمة_مساعدة = شريط_القوائم.addMenu("مساعدة")
        قائمة_مساعدة.addAction("دليل المستخدم", self.دليل_المستخدم)
        قائمة_مساعدة.addAction("الدعم الفني", self.الدعم_الفني)
        
        # قائمة تخصيص
        قائمة_تخصيص = شريط_القوائم.addMenu("تخصيص")
        قائمة_تخصيص.addAction("إعدادات العرض", self.إعدادات_العرض)
        قائمة_تخصيص.addAction("إعدادات الطباعة", self.إعدادات_الطباعة)
    
    def إنشاء_القائمة_الجانبية(self):
        """
        دالة إنشاء القائمة الجانبية الرأسية
        """
        # إطار القائمة الجانبية
        self.إطار_القائمة_الجانبية = QFrame()
        self.إطار_القائمة_الجانبية.setFixedWidth(150)
        self.إطار_القائمة_الجانبية.setObjectName("sidebar")
        
        # تخطيط القائمة الجانبية
        تخطيط_القائمة = QVBoxLayout(self.إطار_القائمة_الجانبية)
        تخطيط_القائمة.setContentsMargins(5, 10, 5, 10)
        تخطيط_القائمة.setSpacing(5)
        
        # زر طي/فتح القائمة
        self.زر_طي_القائمة = QPushButton("☰")
        self.زر_طي_القائمة.setFixedHeight(40)
        self.زر_طي_القائمة.setObjectName("toggle_button")
        self.زر_طي_القائمة.clicked.connect(self.طي_فتح_القائمة)
        تخطيط_القائمة.addWidget(self.زر_طي_القائمة)
        
        # أزرار القائمة الجانبية
        أزرار_القائمة = [
            ("🏠", "الشاشة الرئيسية", "الرئيسية"),
            ("🛒", "نقطة بيع", "نقطة بيع"),
            ("👥", "العملاء", "العملاء"),
            ("📦", "المخزن", "المخزن"),
            ("👨‍💼", "الموظفين", "الموظفين"),
            ("💰", "المصروفات", "المصروفات"),
            ("📋", "الديون والأقساط", "الأقساط"),
            ("🏪", "الموردين", "الموردين"),
            ("🏦", "البنوك", "البنوك"),
            ("🧾", "الفواتير", "الفواتير"),
            ("📄", "العقود", "العقود"),
            ("📊", "تقارير مالية", "التقارير"),
            ("⚙️", "إعدادات", "الإعدادات")
        ]
        
        self.أزرار_القائمة = {}
        
        for أيقونة, نص, مفتاح in أزرار_القائمة:
            زر = QPushButton()
            زر.setFixedSize(140, 60)
            زر.setObjectName("sidebar_button")
            
            # تخطيط الزر
            تخطيط_زر = QVBoxLayout(زر)
            تخطيط_زر.setContentsMargins(5, 5, 5, 5)
            
            # الأيقونة
            تسمية_أيقونة = QLabel(أيقونة)
            تسمية_أيقونة.setAlignment(Qt.AlignCenter)
            تسمية_أيقونة.setStyleSheet("font-size: 24px;")
            
            # النص
            تسمية_نص = QLabel(نص)
            تسمية_نص.setAlignment(Qt.AlignCenter)
            تسمية_نص.setStyleSheet("font-size: 10px; font-weight: bold;")
            تسمية_نص.setWordWrap(True)
            
            تخطيط_زر.addWidget(تسمية_أيقونة)
            تخطيط_زر.addWidget(تسمية_نص)
            
            # ربط الزر بالوظيفة
            زر.clicked.connect(lambda checked, key=مفتاح: self.تغيير_الصفحة(key))
            
            self.أزرار_القائمة[مفتاح] = زر
            تخطيط_القائمة.addWidget(زر)
        
        # مساحة فارغة في الأسفل
        تخطيط_القائمة.addStretch()
        
        # إضافة القائمة للتخطيط الرئيسي
        self.التخطيط_الرئيسي.addWidget(self.إطار_القائمة_الجانبية)
    
    def إنشاء_المحتوى_الرئيسي(self):
        """
        دالة إنشاء منطقة المحتوى الرئيسي
        """
        # إطار المحتوى الرئيسي
        self.إطار_المحتوى = QFrame()
        self.إطار_المحتوى.setObjectName("main_content")
        
        # تخطيط المحتوى الرئيسي
        تخطيط_المحتوى = QVBoxLayout(self.إطار_المحتوى)
        تخطيط_المحتوى.setContentsMargins(10, 10, 10, 10)
        تخطيط_المحتوى.setSpacing(10)
        
        # شريط الفلاتر والبحث
        #self.إنشاء_شريط_الفلاتر(تخطيط_المحتوى)
        
        # شريط الإجراءات
        #self.إنشاء_شريط_الإجراءات(تخطيط_المحتوى)
        
        # منطقة عرض البيانات
        self.منطقة_البيانات = QStackedWidget()
        self.منطقة_البيانات.setObjectName("data_area")
        
        # إضافة صفحة ترحيبية افتراضية
        صفحة_ترحيبية = QWidget()
        تخطيط_ترحيبي = QVBoxLayout(صفحة_ترحيبية)
        
        تسمية_ترحيب = QLabel("مرحباً بك في نظام إدارة المبيعات والمخزون")
        تسمية_ترحيب.setAlignment(Qt.AlignCenter)
        تسمية_ترحيب.setStyleSheet("font-size: 24px; font-weight: bold; color: #2c3e50; margin: 50px;")
        
        تخطيط_ترحيبي.addWidget(تسمية_ترحيب)
        self.منطقة_البيانات.addWidget(صفحة_ترحيبية)
        
        تخطيط_المحتوى.addWidget(self.منطقة_البيانات)
        
        # إضافة المحتوى للتخطيط الرئيسي
        self.التخطيط_الرئيسي.addWidget(self.إطار_المحتوى)
    
    # def إنشاء_شريط_الفلاتر(self, تخطيط_المحتوى):
    #     """
    #     دالة إنشاء شريط الفلاتر والبحث
    #     """
    #     # إطار الفلاتر
    #     إطار_الفلاتر = QFrame()
    #     إطار_الفلاتر.setFixedHeight(60)
    #     إطار_الفلاتر.setObjectName("filters_bar")
        
    #     تخطيط_الفلاتر = QHBoxLayout(إطار_الفلاتر)
    #     تخطيط_الفلاتر.setContentsMargins(10, 5, 10, 5)
        
    #     # حقل البحث
    #     self.حقل_البحث = QLineEdit()
    #     self.حقل_البحث.setPlaceholderText("البحث...")
    #     self.حقل_البحث.setFixedHeight(35)
    #     self.حقل_البحث.setObjectName("search_field")
        
    #     # زر البحث
    #     زر_البحث = QPushButton("🔍")
    #     زر_البحث.setFixedSize(35, 35)
    #     زر_البحث.setObjectName("search_button")
        
    #     # فلاتر متقدمة
    #     زر_فلاتر = QPushButton("فلاتر متقدمة")
    #     زر_فلاتر.setFixedHeight(35)
    #     زر_فلاتر.setObjectName("filter_button")
        
    #     تخطيط_الفلاتر.addWidget(QLabel("البحث:"))
    #     تخطيط_الفلاتر.addWidget(self.حقل_البحث)
    #     تخطيط_الفلاتر.addWidget(زر_البحث)
    #     تخطيط_الفلاتر.addStretch()
    #     تخطيط_الفلاتر.addWidget(زر_فلاتر)
        
    #     تخطيط_المحتوى.addWidget(إطار_الفلاتر)
    
    # def إنشاء_شريط_الإجراءات(self, تخطيط_المحتوى):
    #     """
    #     دالة إنشاء شريط الإجراءات
    #     """
    #     # إطار الإجراءات
    #     إطار_الإجراءات = QFrame()
    #     إطار_الإجراءات.setFixedHeight(100)
    #     إطار_الإجراءات.setObjectName("actions_bar")
        
    #     تخطيط_الإجراءات = QHBoxLayout(إطار_الإجراءات)
    #     تخطيط_الإجراءات.setContentsMargins(10, 5, 10, 5)
    #     تخطيط_الإجراءات.setSpacing(10)
        
    #     # أزرار الإجراءات الأساسية
    #     إجراءات_أساسية = [
    #         ("➕", "إضافة", "add", "#27ae60"),
    #         ("✏️", "تعديل", "edit", "#8e44ad"),
    #         ("🗑️", "حذف", "delete", "#e74c3c"),
    #         ("🖨️", "طباعة", "print", "#7f8c8d"),
    #         ("📊", "تقرير", "report", "#3498db"),
    #         ("💰", "مصروف", "expense", "#f39c12"),
    #         ("📋", "حالة", "status", "#f1c40f")
    #     ]
        
    #     self.أزرار_الإجراءات = {}
        
    #     for أيقونة, نص, مفتاح, لون in إجراءات_أساسية:
    #         زر = self.إنشاء_زر_إجراء(أيقونة, نص, لون)
    #         self.أزرار_الإجراءات[مفتاح] = زر
    #         تخطيط_الإجراءات.addWidget(زر)
        
    #     تخطيط_الإجراءات.addStretch()
    #     تخطيط_المحتوى.addWidget(إطار_الإجراءات)
    
    def إنشاء_زر_إجراء(self, أيقونة, نص, لون):
        """
        دالة إنشاء زر إجراء مخصص
        """
        زر = QPushButton()
        زر.setFixedSize(80, 80)
        زر.setObjectName("action_button")
        
        تخطيط_زر = QVBoxLayout(زر)
        تخطيط_زر.setContentsMargins(5, 5, 5, 5)
        
        # الأيقونة
        تسمية_أيقونة = QLabel(أيقونة)
        تسمية_أيقونة.setAlignment(Qt.AlignCenter)
        تسمية_أيقونة.setStyleSheet("font-size: 32px;")
        
        # النص
        تسمية_نص = QLabel(نص)
        تسمية_نص.setAlignment(Qt.AlignCenter)
        تسمية_نص.setStyleSheet("font-size: 10px; font-weight: bold;")
        
        تخطيط_زر.addWidget(تسمية_أيقونة)
        تخطيط_زر.addWidget(تسمية_نص)
        
        # تطبيق اللون
        زر.setStyleSheet(f"""
            QPushButton#action_button {{
                border: 2px solid {لون};
                border-radius: 8px;
                background-color: white;
            }}
            QPushButton#action_button:hover {{
                background-color: {لون};
                color: white;
            }}
            QPushButton#action_button:pressed {{
                background-color: {لون};
                border: 3px solid {لون};
            }}
        """)
        
        return زر

    def تطبيق_الأنماط(self):
        """
        دالة تطبيق الأنماط CSS للتطبيق
        """
        # تطبيق الأنماط الرئيسية
        نمط_رئيسي = get_main_stylesheet()

        # إضافة أنماط متجاوبة
        نمط_متجاوب = get_responsive_styles()

        # أنماط إضافية خاصة بالتطبيق
        أنماط_إضافية = f"""
        /* أنماط خاصة بالتطبيق */
        QLineEdit#search_field {{
            border: 2px solid #bdc3c7;
            border-radius: 8px;
            padding: 10px;
            font-size: 14px;
            background-color: {COLORS['white']};
        }}

        QLineEdit#search_field:focus {{
            border-color: {COLORS['info']};
            background-color: #f0f8ff;
        }}

        QPushButton#search_button {{
            background-color: {COLORS['info']};
            color: {COLORS['white']};
            border: none;
            border-radius: 8px;
            font-size: 16px;
            padding: 8px 12px;
            font-weight: bold;
        }}

        QPushButton#search_button:hover {{
            background-color: #2980b9;
        }}

        QPushButton#filter_button {{
            background-color: {COLORS['purple']};
            color: {COLORS['white']};
            border: none;
            border-radius: 8px;
            padding: 8px 15px;
            font-weight: bold;
        }}

        QPushButton#filter_button:hover {{
            background-color: #8e44ad;
        }}

        /* أنماط أزرار الإجراءات المحسنة */
        QPushButton#complete_sale_button {{
            background-color: {COLORS['success']};
            color: {COLORS['white']};
            border: none;
            border-radius: 8px;
            font-size: 14px;
            padding: 12px 20px;
            font-weight: bold;
        }}

        QPushButton#complete_sale_button:hover {{
            background-color: #229954;
        }}

        QPushButton#clear_cart_button {{
            background-color: {COLORS['danger']};
            color: {COLORS['white']};
            border: none;
            border-radius: 8px;
            font-size: 14px;
            padding: 12px 20px;
            font-weight: bold;
        }}

        QPushButton#clear_cart_button:hover {{
            background-color: #c0392b;
        }}

        QPushButton#print_button {{
            background-color: {COLORS['info']};
            color: {COLORS['white']};
            border: none;
            border-radius: 8px;
            font-size: 14px;
            padding: 12px 20px;
            font-weight: bold;
        }}

        QPushButton#print_button:hover {{
            background-color: #2980b9;
        }}

        QPushButton#advanced_search_button {{
            background-color: {COLORS['purple']};
            color: {COLORS['white']};
            border: none;
            border-radius: 8px;
            padding: 8px 15px;
            font-weight: bold;
        }}

        QPushButton#advanced_search_button:hover {{
            background-color: #8e44ad;
        }}

        QPushButton#new_customer_button {{
            background-color: {COLORS['success']};
            color: {COLORS['white']};
            border: none;
            border-radius: 8px;
            padding: 8px 15px;
            font-weight: bold;
        }}

        QPushButton#new_customer_button:hover {{
            background-color: #229954;
        }}

        QPushButton#search_invoice_button {{
            background-color: {COLORS['warning']};
            color: {COLORS['white']};
            border: none;
            border-radius: 8px;
            padding: 8px 15px;
            font-weight: bold;
        }}

        QPushButton#search_invoice_button:hover {{
            background-color: #d68910;
        }}

        /* تحسينات إضافية للجداول */
        QTableWidget::item:alternate {{
            background-color: #f8f9fa;
        }}

        QTableWidget::item:selected:alternate {{
            background-color: {COLORS['info']};
        }}

        /* تحسينات للنوافذ المنبثقة */
        QDialog {{
            background-color: {COLORS['white']};
            border: 2px solid {COLORS['secondary']};
            border-radius: 10px;
        }}

        QDialog QLabel {{
            color: {COLORS['dark']};
        }}

        /* تحسينات للرسائل */
        QMessageBox {{
            background-color: {COLORS['white']};
            color: {COLORS['dark']};
        }}

        QMessageBox QPushButton {{
            background-color: {COLORS['info']};
            color: {COLORS['white']};
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: bold;
            min-width: 80px;
            margin: 5px;
        }}

        QMessageBox QPushButton:hover {{
            background-color: #2980b9;
        }}
        """

        # دمج جميع الأنماط
       # النمط_النهائي = نمط_رئيسي + نمط_متجاوب + أنماط_إضافية
        النمط_النهائي = نمط_رئيسي 
     

        self.setStyleSheet(النمط_النهائي)

    def طي_فتح_القائمة(self):
        """
        دالة طي وفتح القائمة الجانبية
        """
        if self.إطار_القائمة_الجانبية.width() == 150:
            self.إطار_القائمة_الجانبية.setFixedWidth(50)
            # إخفاء النصوص
            for زر in self.أزرار_القائمة.values():
                تخطيط = زر.layout()
                if تخطيط and تخطيط.count() > 1:
                    تخطيط.itemAt(1).widget().hide()
        else:
            self.إطار_القائمة_الجانبية.setFixedWidth(150)
            # إظهار النصوص
            for زر in self.أزرار_القائمة.values():
                تخطيط = زر.layout()
                if تخطيط and تخطيط.count() > 1:
                    تخطيط.itemAt(1).widget().show()

    def تغيير_الصفحة(self, صفحة):
        """
        دالة تغيير الصفحة المعروضة
        """
        # إزالة الصفحة الحالية إذا كانت موجودة
        while self.منطقة_البيانات.count() > 0:
            widget = self.منطقة_البيانات.widget(0)
            self.منطقة_البيانات.removeWidget(widget)
            widget.deleteLater()

        # إضافة الصفحة الجديدة
        if صفحة == "نقطة بيع":
            واجهة_نقطة = واجهة_نقطة_البيع()
            self.منطقة_البيانات.addWidget(واجهة_نقطة)
        elif صفحة == "العملاء":
            واجهة_عملاء = واجهة_إدارة_العملاء()
            self.منطقة_البيانات.addWidget(واجهة_عملاء)
        elif صفحة == "المخزن":
            واجهة_مخزون = واجهة_إدارة_المخزون()
            self.منطقة_البيانات.addWidget(واجهة_مخزون)
        elif صفحة == "الموردين":
            واجهة_موردين = واجهة_إدارة_الموردين()
            self.منطقة_البيانات.addWidget(واجهة_موردين)
        elif صفحة == "الموظفين":
            واجهة_موظفين = واجهة_الموظفين()
            self.منطقة_البيانات.addWidget(واجهة_موظفين)
        elif صفحة == "المصروفات":
            واجهة_مصروفات = واجهة_المصروفات()
            self.منطقة_البيانات.addWidget(واجهة_مصروفات)
        elif صفحة == "البنوك":
            واجهة_بنوك = واجهة_البنوك()
            self.منطقة_البيانات.addWidget(واجهة_بنوك)
        elif صفحة == "العقود":
            واجهة_عقود = واجهة_العقود()
            self.منطقة_البيانات.addWidget(واجهة_عقود)
        elif صفحة == "الأقساط":
            واجهة_أقساط = واجهة_الأقساط()
            self.منطقة_البيانات.addWidget(واجهة_أقساط)
        elif صفحة == "التقارير":
            واجهة_تقارير = واجهة_التقارير_المالية()
            self.منطقة_البيانات.addWidget(واجهة_تقارير)
        elif صفحة == "الإعدادات":
            واجهة_إعدادات = واجهة_الإعدادات()
            self.منطقة_البيانات.addWidget(واجهة_إعدادات)
        else:
            # الصفحة الافتراضية
            صفحة_افتراضية = QWidget()
            تخطيط = QVBoxLayout(صفحة_افتراضية)
            تسمية = QLabel(f"واجهة {صفحة} - قيد التطوير")
            تسمية.setAlignment(Qt.AlignCenter)
            تسمية.setStyleSheet("font-size: 18px; color: #7f8c8d;")
            تخطيط.addWidget(تسمية)
            self.منطقة_البيانات.addWidget(صفحة_افتراضية)

        print(f"تم تغيير الصفحة إلى: {صفحة}")

    # دوال القوائم العلوية
    def ملف_جديد(self):
        """دالة إنشاء ملف جديد"""
        print("ملف جديد")

    def ملف_فتح(self):
        """دالة فتح ملف"""
        print("فتح ملف")

    def ملف_حفظ(self):
        """دالة حفظ ملف"""
        print("حفظ ملف")

    def حول_البرنامج(self):
        """دالة عرض معلومات البرنامج"""
        QMessageBox.about(self, "حول البرنامج",
                         "نظام إدارة المبيعات والمخزون\nالإصدار 1.0\nتطوير: فريق التطوير")

    def معلومات_النظام(self):
        """دالة عرض معلومات النظام"""
        print("معلومات النظام")

    def تسجيل_دخول(self):
        """دالة تسجيل الدخول"""
        print("تسجيل دخول")

    def تسجيل_خروج(self):
        """دالة تسجيل الخروج"""
        print("تسجيل خروج")

    def تغيير_كلمة_المرور(self):
        """دالة تغيير كلمة المرور"""
        print("تغيير كلمة المرور")

    def دليل_المستخدم(self):
        """دالة عرض دليل المستخدم"""
        print("دليل المستخدم")

    def الدعم_الفني(self):
        """دالة الدعم الفني"""
        print("الدعم الفني")

    def إعدادات_العرض(self):
        """دالة إعدادات العرض"""
        print("إعدادات العرض")

    def إعدادات_الطباعة(self):
        """دالة إعدادات الطباعة"""
        print("إعدادات الطباعة")

class التطبيق_الرئيسي(QApplication):
    """
    كلاس التطبيق الرئيسي
    """

    def __init__(self, argv):
        """
        دالة التهيئة للتطبيق
        """
        super().__init__(argv)
        

        # إعداد التطبيق
        self.setApplicationName("نظام إدارة المبيعات والمخزون")
        self.setApplicationVersion("1.0")
        self.setOrganizationName("شركة التطوير")

        # تهيئة قاعدة البيانات
        if not تهيئة_قاعدة_البيانات():
            QMessageBox.critical(None, "خطأ", "فشل في تهيئة قاعدة البيانات")
            sys.exit(1)

        # إنشاء النافذة الرئيسية
        self.النافذة_الرئيسية = النافذة_الرئيسية()
        self.النافذة_الرئيسية.show()

def main():
    """
    دالة تشغيل التطبيق الرئيسية
    """
    app = التطبيق_الرئيسي(sys.argv)
    app.setStyle("WindowsVista")  # تطبيق الستايل المخصص
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
