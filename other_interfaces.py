# -*- coding: utf-8 -*-
"""
الواجهات المتبقية للتطبيق
تحتوي على واجهات الموظفين والمصروفات والبنوك والعقود والأقساط والتقارير المالية
"""

from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *
from datetime import datetime
from db import قاعدة_البيانات

class واجهة_الموظفين(QWidget):
    """
    واجهة إدارة الموظفين الشاملة
    """

    def __init__(self):
        """
        دالة التهيئة لواجهة الموظفين
        """
        super().__init__()
        self.قاعدة_البيانات = قاعدة_البيانات()
        self.قاعدة_البيانات.اتصال_قاعدة_البيانات()
        self.قاعدة_البيانات.cursor.execute(f"USE {self.قاعدة_البيانات.database}")

        self.الموظف_المحدد = None
        self.إعداد_الواجهة()
        self.تحميل_البيانات()

    def إعداد_الواجهة(self):
        """
        دالة إعداد واجهة الموظفين
        """
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(20, 20, 20, 20)
        التخطيط_الرئيسي.setSpacing(20)

        # عنوان الصفحة
        عنوان = QLabel("إدارة الموظفين")
        عنوان.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            padding: 15px;
            background-color: #ecf0f1;
            border-radius: 8px;
            border-left: 5px solid #9b59b6;
        """)
        التخطيط_الرئيسي.addWidget(عنوان)

        # شريط الأدوات
        self.إنشاء_شريط_الأدوات(التخطيط_الرئيسي)

        # جدول الموظفين
        self.إنشاء_جدول_الموظفين(التخطيط_الرئيسي)

    def إنشاء_شريط_الأدوات(self, التخطيط_الرئيسي):
        """
        إنشاء شريط الأدوات مع الأزرار الرئيسية
        """
        إطار_الأدوات = QFrame()
        إطار_الأدوات.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 1px solid #bdc3c7;
                border-radius: 8px;
                padding: 10px;
            }
        """)

        تخطيط_الأدوات = QHBoxLayout(إطار_الأدوات)
        تخطيط_الأدوات.setSpacing(10)

        # زر إضافة موظف
        زر_إضافة = QPushButton("إضافة موظف")
        زر_إضافة.setIcon(QIcon("icons/add_employee.png"))
        زر_إضافة.clicked.connect(self.إضافة_موظف)
        زر_إضافة.setStyleSheet(self.نمط_الزر("#27ae60"))

        # زر تعديل
        زر_تعديل = QPushButton("تعديل")
        زر_تعديل.setIcon(QIcon("icons/edit.png"))
        زر_تعديل.clicked.connect(self.تعديل_الموظف)
        زر_تعديل.setStyleSheet(self.نمط_الزر("#f39c12"))

        # زر حذف
        زر_حذف = QPushButton("حذف")
        زر_حذف.setIcon(QIcon("icons/delete.png"))
        زر_حذف.clicked.connect(self.حذف_الموظف)
        زر_حذف.setStyleSheet(self.نمط_الزر("#e74c3c"))

        # زر تحديث
        زر_تحديث = QPushButton("تحديث")
        زر_تحديث.setIcon(QIcon("icons/refresh.png"))
        زر_تحديث.clicked.connect(self.تحديث_البيانات)
        زر_تحديث.setStyleSheet(self.نمط_الزر("#9b59b6"))

        # زر تقرير الموظفين
        زر_تقرير = QPushButton("تقرير الموظفين")
        زر_تقرير.setIcon(QIcon("icons/report.png"))
        زر_تقرير.clicked.connect(self.تقرير_الموظفين)
        زر_تقرير.setStyleSheet(self.نمط_الزر("#3498db"))

        تخطيط_الأدوات.addWidget(زر_إضافة)
        تخطيط_الأدوات.addWidget(زر_تعديل)
        تخطيط_الأدوات.addWidget(زر_حذف)
        تخطيط_الأدوات.addWidget(زر_تحديث)
        تخطيط_الأدوات.addWidget(زر_تقرير)
        تخطيط_الأدوات.addStretch()

        التخطيط_الرئيسي.addWidget(إطار_الأدوات)

    def إنشاء_جدول_الموظفين(self, التخطيط_الرئيسي):
        """
        إنشاء جدول الموظفين
        """
        إطار_الجدول = QFrame()
        إطار_الجدول.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 1px solid #bdc3c7;
                border-radius: 8px;
            }
        """)

        تخطيط_الجدول = QVBoxLayout(إطار_الجدول)
        تخطيط_الجدول.setContentsMargins(15, 15, 15, 15)

        # عنوان الجدول
        عنوان_الجدول = QLabel("قائمة الموظفين")
        عنوان_الجدول.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            padding: 10px;
            background-color: #ecf0f1;
            border-radius: 5px;
        """)
        تخطيط_الجدول.addWidget(عنوان_الجدول)

        # شريط البحث
        self.حقل_البحث = QLineEdit()
        self.حقل_البحث.setPlaceholderText("البحث في الموظفين...")
        self.حقل_البحث.textChanged.connect(self.بحث_في_الموظفين)
        self.حقل_البحث.setStyleSheet("""
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 12px;
                background-color: #ffffff;
            }
            QLineEdit:focus {
                border-color: #9b59b6;
            }
        """)
        تخطيط_الجدول.addWidget(self.حقل_البحث)

        # جدول الموظفين
        self.جدول_الموظفين = QTableWidget()
        self.جدول_الموظفين.setColumnCount(7)
        self.جدول_الموظفين.setHorizontalHeaderLabels([
            "رقم الموظف", "الاسم الكامل", "المنصب", "رقم الهاتف",
            "الراتب الأساسي", "الحالة", "تاريخ التوظيف"
        ])

        # تنسيق الجدول
        self.جدول_الموظفين.setLayoutDirection(Qt.RightToLeft)
        self.جدول_الموظفين.setAlternatingRowColors(True)
        self.جدول_الموظفين.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.جدول_الموظفين.setSelectionMode(QAbstractItemView.SingleSelection)
        self.جدول_الموظفين.horizontalHeader().setStretchLastSection(True)

        # ربط إشارة التحديد والدبل كليك
        self.جدول_الموظفين.itemSelectionChanged.connect(self.عند_تحديد_موظف)
        self.جدول_الموظفين.itemDoubleClicked.connect(self.تعديل_الموظف)

        self.جدول_الموظفين.setStyleSheet("""
            QTableWidget {
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                background-color: #ffffff;
                gridline-color: #ecf0f1;
                font-size: 11px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background-color: #9b59b6;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)

        تخطيط_الجدول.addWidget(self.جدول_الموظفين)
        التخطيط_الرئيسي.addWidget(إطار_الجدول)



    def نمط_الزر(self, لون_أساسي):
        """
        نمط موحد للأزرار
        """
        return f"""
            QPushButton {{
                background-color: {لون_أساسي};
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background-color: {لون_أساسي}dd;
            }}
            QPushButton:pressed {{
                background-color: {لون_أساسي}aa;
            }}
        """

    def نمط_الحقل(self):
        """
        نمط موحد للحقول النصية
        """
        return """
            QLineEdit, QDoubleSpinBox, QDateEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 12px;
                background-color: #ffffff;
            }
            QLineEdit:focus, QDoubleSpinBox:focus, QDateEdit:focus {
                border-color: #9b59b6;
                background-color: #f8f9fa;
            }
        """

    def تحميل_البيانات(self):
        """
        تحميل بيانات الموظفين من قاعدة البيانات
        """
        try:
            استعلام = """
                SELECT رقم_الموظف, الاسم_الكامل, المنصب, رقم_الهاتف,
                       الراتب_الأساسي, حالة_النشاط, تاريخ_التوظيف
                FROM الموظفين
                ORDER BY تاريخ_الإنشاء DESC
            """
            self.قاعدة_البيانات.cursor.execute(استعلام)
            الموظفين = self.قاعدة_البيانات.cursor.fetchall()

            # مسح الجدول
            self.جدول_الموظفين.setRowCount(0)

            # إضافة البيانات للجدول
            for صف, موظف in enumerate(الموظفين):
                self.جدول_الموظفين.insertRow(صف)

                # رقم الموظف
                self.جدول_الموظفين.setItem(صف, 0, QTableWidgetItem(str(موظف[0])))

                # الاسم الكامل
                self.جدول_الموظفين.setItem(صف, 1, QTableWidgetItem(موظف[1]))

                # المنصب
                self.جدول_الموظفين.setItem(صف, 2, QTableWidgetItem(موظف[2] or "غير محدد"))

                # رقم الهاتف
                self.جدول_الموظفين.setItem(صف, 3, QTableWidgetItem(موظف[3] or "غير محدد"))

                # الراتب الأساسي
                راتب = f"{موظف[4]:,.2f} ريال" if موظف[4] else "0.00 ريال"
                self.جدول_الموظفين.setItem(صف, 4, QTableWidgetItem(راتب))

                # الحالة
                حالة = "نشط" if موظف[5] else "غير نشط"
                عنصر_الحالة = QTableWidgetItem(حالة)
                if موظف[5]:
                    عنصر_الحالة.setForeground(QBrush(QColor("#27ae60")))
                else:
                    عنصر_الحالة.setForeground(QBrush(QColor("#e74c3c")))
                self.جدول_الموظفين.setItem(صف, 5, عنصر_الحالة)

                # تاريخ التوظيف
                تاريخ = موظف[6].strftime("%Y-%m-%d") if موظف[6] else "غير محدد"
                self.جدول_الموظفين.setItem(صف, 6, QTableWidgetItem(تاريخ))

                # حفظ رقم الموظف في البيانات المخفية
                self.جدول_الموظفين.item(صف, 0).setData(Qt.UserRole, موظف[0])

            # تعديل عرض الأعمدة
            self.جدول_الموظفين.resizeColumnsToContents()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل البيانات: {str(e)}")

    def عند_تحديد_موظف(self):
        """
        عند تحديد موظف من الجدول
        """
        الصف_المحدد = self.جدول_الموظفين.currentRow()
        if الصف_المحدد >= 0:
            رقم_الموظف = self.جدول_الموظفين.item(الصف_المحدد, 0).data(Qt.UserRole)
            if رقم_الموظف:
                self.الموظف_المحدد = رقم_الموظف
                self.تحميل_تفاصيل_الموظف(رقم_الموظف)
            else:
                self.مسح_النموذج()



    def إضافة_موظف(self):
        """
        إضافة موظف جديد
        """
        نافذة_إضافة = نافذة_موظف(self, "إضافة")
        if نافذة_إضافة.exec_() == QDialog.Accepted:
            self.تحميل_البيانات()

    def تعديل_الموظف(self):
        """
        تعديل الموظف المحدد
        """
        if not self.الموظف_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد موظف للتعديل")
            return

        نافذة_تعديل = نافذة_موظف(self, "تعديل", self.الموظف_المحدد)
        if نافذة_تعديل.exec_() == QDialog.Accepted:
            self.تحميل_البيانات()

    def حذف_الموظف(self):
        """
        حذف الموظف المحدد
        """
        if not self.الموظف_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد موظف للحذف")
            return

        # التحقق من وجود ارتباطات
        if self.التحقق_من_الارتباطات(self.الموظف_المحدد):
            QMessageBox.warning(self, "تحذير",
                              "لا يمكن حذف هذا الموظف لوجود معاملات مرتبطة به.\n"
                              "يرجى إلغاء تفعيل الموظف بدلاً من حذفه.")
            return

        # تأكيد الحذف
        رد = QMessageBox.question(self, "تأكيد الحذف",
                                 "هل أنت متأكد من حذف هذا الموظف؟\n"
                                 "هذا الإجراء لا يمكن التراجع عنه.",
                                 QMessageBox.Yes | QMessageBox.No)

        if رد == QMessageBox.Yes:
            try:
                استعلام = "DELETE FROM الموظفين WHERE رقم_الموظف = %s"
                self.قاعدة_البيانات.cursor.execute(استعلام, (self.الموظف_المحدد,))
                self.قاعدة_البيانات.connection.commit()

                QMessageBox.information(self, "نجح", "تم حذف الموظف بنجاح")
                self.تحديث_البيانات()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف الموظف: {str(e)}")

    def التحقق_من_الارتباطات(self, رقم_الموظف):
        """
        التحقق من وجود ارتباطات للموظف
        """
        try:
            # التحقق من فواتير البيع
            self.قاعدة_البيانات.cursor.execute(
                "SELECT COUNT(*) FROM فواتير_البيع WHERE رقم_الموظف = %s",
                (رقم_الموظف,)
            )
            عدد_الفواتير = self.قاعدة_البيانات.cursor.fetchone()[0]

            # التحقق من المصروفات
            self.قاعدة_البيانات.cursor.execute(
                "SELECT COUNT(*) FROM المصروفات WHERE رقم_الموظف = %s",
                (رقم_الموظف,)
            )
            عدد_المصروفات = self.قاعدة_البيانات.cursor.fetchone()[0]

            return عدد_الفواتير > 0 or عدد_المصروفات > 0

        except Exception as e:
            print(f"خطأ في التحقق من الارتباطات: {str(e)}")
            return True  # في حالة الخطأ، نمنع الحذف للأمان

    def تحديث_البيانات(self):
        """
        تحديث جميع البيانات
        """
        self.تحميل_البيانات()

    def بحث_في_الموظفين(self):
        """
        البحث في الموظفين
        """
        نص_البحث = self.حقل_البحث.text().strip()

        if not نص_البحث:
            self.تحميل_البيانات()
            return

        try:
            استعلام = """
                SELECT رقم_الموظف, الاسم_الكامل, المنصب, رقم_الهاتف,
                       الراتب_الأساسي, حالة_النشاط, تاريخ_التوظيف
                FROM الموظفين
                WHERE الاسم_الكامل LIKE %s OR المنصب LIKE %s OR رقم_الهاتف LIKE %s
                ORDER BY تاريخ_الإنشاء DESC
            """
            نمط_البحث = f"%{نص_البحث}%"
            self.قاعدة_البيانات.cursor.execute(استعلام, (نمط_البحث, نمط_البحث, نمط_البحث))
            الموظفين = self.قاعدة_البيانات.cursor.fetchall()

            # مسح الجدول
            self.جدول_الموظفين.setRowCount(0)

            # إضافة نتائج البحث
            for صف, موظف in enumerate(الموظفين):
                self.جدول_الموظفين.insertRow(صف)

                # إضافة البيانات (نفس منطق تحميل_البيانات)
                self.جدول_الموظفين.setItem(صف, 0, QTableWidgetItem(str(موظف[0])))
                self.جدول_الموظفين.setItem(صف, 1, QTableWidgetItem(موظف[1]))
                self.جدول_الموظفين.setItem(صف, 2, QTableWidgetItem(موظف[2] or "غير محدد"))
                self.جدول_الموظفين.setItem(صف, 3, QTableWidgetItem(موظف[3] or "غير محدد"))

                راتب = f"{موظف[4]:,.2f} ريال" if موظف[4] else "0.00 ريال"
                self.جدول_الموظفين.setItem(صف, 4, QTableWidgetItem(راتب))

                حالة = "نشط" if موظف[5] else "غير نشط"
                عنصر_الحالة = QTableWidgetItem(حالة)
                if موظف[5]:
                    عنصر_الحالة.setForeground(QBrush(QColor("#27ae60")))
                else:
                    عنصر_الحالة.setForeground(QBrush(QColor("#e74c3c")))
                self.جدول_الموظفين.setItem(صف, 5, عنصر_الحالة)

                تاريخ = موظف[6].strftime("%Y-%m-%d") if موظف[6] else "غير محدد"
                self.جدول_الموظفين.setItem(صف, 6, QTableWidgetItem(تاريخ))

                self.جدول_الموظفين.item(صف, 0).setData(Qt.UserRole, موظف[0])

            self.جدول_الموظفين.resizeColumnsToContents()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في البحث: {str(e)}")

    def تقرير_الموظفين(self):
        """
        إنشاء تقرير الموظفين
        """
        QMessageBox.information(self, "تقرير الموظفين", "سيتم تطوير تقرير الموظفين قريباً")

class واجهة_المصروفات(QWidget):
    """
    واجهة إدارة المصروفات الشاملة
    """

    def __init__(self):
        """
        دالة التهيئة لواجهة المصروفات
        """
        super().__init__()
        self.قاعدة_البيانات = قاعدة_البيانات()
        self.قاعدة_البيانات.اتصال_قاعدة_البيانات()
        self.قاعدة_البيانات.cursor.execute(f"USE {self.قاعدة_البيانات.database}")

        self.المصروف_المحدد = None
        self.إعداد_الواجهة()
        self.تحميل_البيانات()
        self.تحميل_الموظفين()

    def إعداد_الواجهة(self):
        """
        دالة إعداد واجهة المصروفات
        """
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(20, 20, 20, 20)
        التخطيط_الرئيسي.setSpacing(20)

        # عنوان الصفحة
        عنوان = QLabel("إدارة المصروفات")
        عنوان.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            padding: 15px;
            background-color: #ecf0f1;
            border-radius: 8px;
            border-left: 5px solid #f39c12;
        """)
        التخطيط_الرئيسي.addWidget(عنوان)

        # شريط الأدوات
        self.إنشاء_شريط_الأدوات(التخطيط_الرئيسي)

        # جدول المصروفات
        self.إنشاء_جدول_المصروفات(التخطيط_الرئيسي)

    def إنشاء_شريط_الأدوات(self, التخطيط_الرئيسي):
        """
        إنشاء شريط الأدوات مع الأزرار الرئيسية
        """
        إطار_الأدوات = QFrame()
        إطار_الأدوات.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 1px solid #bdc3c7;
                border-radius: 8px;
                padding: 10px;
            }
        """)

        تخطيط_الأدوات = QHBoxLayout(إطار_الأدوات)
        تخطيط_الأدوات.setSpacing(10)

        # زر إضافة مصروف
        زر_إضافة = QPushButton("إضافة مصروف")
        زر_إضافة.setIcon(QIcon("icons/add_expense.png"))
        زر_إضافة.clicked.connect(self.إضافة_مصروف)
        زر_إضافة.setStyleSheet(self.نمط_الزر("#27ae60"))

        # زر تعديل
        زر_تعديل = QPushButton("تعديل")
        زر_تعديل.setIcon(QIcon("icons/edit.png"))
        زر_تعديل.clicked.connect(self.تعديل_المصروف)
        زر_تعديل.setStyleSheet(self.نمط_الزر("#f39c12"))

        # زر حذف
        زر_حذف = QPushButton("حذف")
        زر_حذف.setIcon(QIcon("icons/delete.png"))
        زر_حذف.clicked.connect(self.حذف_المصروف)
        زر_حذف.setStyleSheet(self.نمط_الزر("#e74c3c"))

        # زر تحديث
        زر_تحديث = QPushButton("تحديث")
        زر_تحديث.setIcon(QIcon("icons/refresh.png"))
        زر_تحديث.clicked.connect(self.تحديث_البيانات)
        زر_تحديث.setStyleSheet(self.نمط_الزر("#9b59b6"))

        # زر تقرير المصروفات
        زر_تقرير = QPushButton("تقرير المصروفات")
        زر_تقرير.setIcon(QIcon("icons/report.png"))
        زر_تقرير.clicked.connect(self.تقرير_المصروفات)
        زر_تقرير.setStyleSheet(self.نمط_الزر("#3498db"))

        تخطيط_الأدوات.addWidget(زر_إضافة)
        تخطيط_الأدوات.addWidget(زر_تعديل)
        تخطيط_الأدوات.addWidget(زر_حذف)
        تخطيط_الأدوات.addWidget(زر_تحديث)
        تخطيط_الأدوات.addWidget(زر_تقرير)
        تخطيط_الأدوات.addStretch()

        التخطيط_الرئيسي.addWidget(إطار_الأدوات)

    def إنشاء_جدول_المصروفات(self, التخطيط_الرئيسي):
        """
        إنشاء جدول المصروفات
        """
        إطار_الجدول = QFrame()
        إطار_الجدول.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 1px solid #bdc3c7;
                border-radius: 8px;
            }
        """)

        تخطيط_الجدول = QVBoxLayout(إطار_الجدول)
        تخطيط_الجدول.setContentsMargins(15, 15, 15, 15)

        # عنوان الجدول مع إجمالي المصروفات
        تخطيط_العنوان = QHBoxLayout()

        عنوان_الجدول = QLabel("قائمة المصروفات")
        عنوان_الجدول.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            padding: 10px;
            background-color: #ecf0f1;
            border-radius: 5px;
        """)

        self.تسمية_الإجمالي = QLabel("الإجمالي: 0.00 ريال")
        self.تسمية_الإجمالي.setStyleSheet("""
            font-size: 14px;
            font-weight: bold;
            color: #e74c3c;
            padding: 10px;
            background-color: #fff5f5;
            border: 1px solid #e74c3c;
            border-radius: 5px;
        """)

        تخطيط_العنوان.addWidget(عنوان_الجدول)
        تخطيط_العنوان.addStretch()
        تخطيط_العنوان.addWidget(self.تسمية_الإجمالي)

        تخطيط_الجدول.addLayout(تخطيط_العنوان)

        # شريط البحث والفلترة
        تخطيط_البحث = QHBoxLayout()

        self.حقل_البحث = QLineEdit()
        self.حقل_البحث.setPlaceholderText("البحث في المصروفات...")
        self.حقل_البحث.textChanged.connect(self.بحث_في_المصروفات)
        self.حقل_البحث.setStyleSheet("""
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 12px;
                background-color: #ffffff;
            }
            QLineEdit:focus {
                border-color: #f39c12;
            }
        """)

        # فلتر التاريخ
        self.تاريخ_من = QDateEdit()
        self.تاريخ_من.setDate(QDate.currentDate().addDays(-30))
        self.تاريخ_من.setCalendarPopup(True)
        self.تاريخ_من.dateChanged.connect(self.فلترة_حسب_التاريخ)

        self.تاريخ_إلى = QDateEdit()
        self.تاريخ_إلى.setDate(QDate.currentDate())
        self.تاريخ_إلى.setCalendarPopup(True)
        self.تاريخ_إلى.dateChanged.connect(self.فلترة_حسب_التاريخ)

        تخطيط_البحث.addWidget(QLabel("البحث:"))
        تخطيط_البحث.addWidget(self.حقل_البحث)
        تخطيط_البحث.addWidget(QLabel("من:"))
        تخطيط_البحث.addWidget(self.تاريخ_من)
        تخطيط_البحث.addWidget(QLabel("إلى:"))
        تخطيط_البحث.addWidget(self.تاريخ_إلى)

        تخطيط_الجدول.addLayout(تخطيط_البحث)

        # جدول المصروفات
        self.جدول_المصروفات = QTableWidget()
        self.جدول_المصروفات.setColumnCount(6)
        self.جدول_المصروفات.setHorizontalHeaderLabels([
            "رقم المصروف", "نوع المصروف", "المبلغ", "تاريخ المصروف", "الموظف", "الوصف"
        ])

        # تنسيق الجدول
        self.جدول_المصروفات.setLayoutDirection(Qt.RightToLeft)
        self.جدول_المصروفات.setAlternatingRowColors(True)
        self.جدول_المصروفات.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.جدول_المصروفات.setSelectionMode(QAbstractItemView.SingleSelection)
        self.جدول_المصروفات.horizontalHeader().setStretchLastSection(True)

        # ربط إشارة التحديد والدبل كليك
        self.جدول_المصروفات.itemSelectionChanged.connect(self.عند_تحديد_مصروف)
        self.جدول_المصروفات.itemDoubleClicked.connect(self.تعديل_المصروف)

        self.جدول_المصروفات.setStyleSheet("""
            QTableWidget {
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                background-color: #ffffff;
                gridline-color: #ecf0f1;
                font-size: 11px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background-color: #f39c12;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)

        تخطيط_الجدول.addWidget(self.جدول_المصروفات)
        التخطيط_الرئيسي.addWidget(إطار_الجدول)

    def إنشاء_منطقة_التفاصيل(self, تخطيط_المحتوى):
        """
        إنشاء منطقة تفاصيل المصروف المحدد
        """
        إطار_التفاصيل = QFrame()
        إطار_التفاصيل.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 1px solid #bdc3c7;
                border-radius: 8px;
            }
        """)
        إطار_التفاصيل.setMinimumWidth(350)
        إطار_التفاصيل.setMaximumWidth(450)

        تخطيط_التفاصيل = QVBoxLayout(إطار_التفاصيل)
        تخطيط_التفاصيل.setContentsMargins(20, 20, 20, 20)
        تخطيط_التفاصيل.setSpacing(15)

        # عنوان التفاصيل
        عنوان_التفاصيل = QLabel("تفاصيل المصروف")
        عنوان_التفاصيل.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            padding: 10px;
            background-color: #ecf0f1;
            border-radius: 5px;
        """)
        تخطيط_التفاصيل.addWidget(عنوان_التفاصيل)

        # نموذج التفاصيل
        self.إنشاء_نموذج_التفاصيل(تخطيط_التفاصيل)

        تخطيط_المحتوى.addWidget(إطار_التفاصيل)

    def إنشاء_نموذج_التفاصيل(self, تخطيط_التفاصيل):
        """
        إنشاء نموذج تفاصيل المصروف
        """
        # مجموعة البيانات الأساسية
        مجموعة_أساسية = QGroupBox("بيانات المصروف")
        مجموعة_أساسية.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2c3e50;
            }
        """)

        تخطيط_أساسي = QFormLayout(مجموعة_أساسية)
        تخطيط_أساسي.setSpacing(10)

        # حقول البيانات
        self.حقل_نوع_المصروف = QLineEdit()
        self.حقل_نوع_المصروف.setPlaceholderText("أدخل نوع المصروف")
        self.حقل_نوع_المصروف.setStyleSheet(self.نمط_الحقل())

        self.حقل_المبلغ = QDoubleSpinBox()
        self.حقل_المبلغ.setRange(0, 999999.99)
        self.حقل_المبلغ.setDecimals(2)
        self.حقل_المبلغ.setSuffix(" ريال")
        self.حقل_المبلغ.setStyleSheet(self.نمط_الحقل())

        self.حقل_تاريخ_المصروف = QDateEdit()
        self.حقل_تاريخ_المصروف.setDate(QDate.currentDate())
        self.حقل_تاريخ_المصروف.setCalendarPopup(True)
        self.حقل_تاريخ_المصروف.setStyleSheet(self.نمط_الحقل())

        self.قائمة_الموظف = QComboBox()
        self.قائمة_الموظف.addItem("-- اختر الموظف --", None)
        self.قائمة_الموظف.setStyleSheet(self.نمط_القائمة())

        self.حقل_الوصف = QTextEdit()
        self.حقل_الوصف.setPlaceholderText("أدخل وصف المصروف")
        self.حقل_الوصف.setMaximumHeight(80)
        self.حقل_الوصف.setStyleSheet("""
            QTextEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 12px;
                background-color: #ffffff;
            }
            QTextEdit:focus {
                border-color: #f39c12;
            }
        """)

        self.حقل_الملاحظات = QTextEdit()
        self.حقل_الملاحظات.setPlaceholderText("أدخل ملاحظات إضافية")
        self.حقل_الملاحظات.setMaximumHeight(80)
        self.حقل_الملاحظات.setStyleSheet(self.حقل_الوصف.styleSheet())

        تخطيط_أساسي.addRow("نوع المصروف:", self.حقل_نوع_المصروف)
        تخطيط_أساسي.addRow("المبلغ:", self.حقل_المبلغ)
        تخطيط_أساسي.addRow("تاريخ المصروف:", self.حقل_تاريخ_المصروف)
        تخطيط_أساسي.addRow("الموظف:", self.قائمة_الموظف)
        تخطيط_أساسي.addRow("الوصف:", self.حقل_الوصف)
        تخطيط_أساسي.addRow("الملاحظات:", self.حقل_الملاحظات)

        تخطيط_التفاصيل.addWidget(مجموعة_أساسية)

        # أزرار الحفظ والإلغاء
        تخطيط_الأزرار = QHBoxLayout()

        زر_حفظ = QPushButton("حفظ")
        زر_حفظ.setIcon(QIcon("icons/save.png"))
        زر_حفظ.clicked.connect(self.حفظ_المصروف)
        زر_حفظ.setStyleSheet(self.نمط_الزر("#27ae60"))

        زر_إلغاء = QPushButton("إلغاء")
        زر_إلغاء.setIcon(QIcon("icons/cancel.png"))
        زر_إلغاء.clicked.connect(self.إلغاء_التعديل)
        زر_إلغاء.setStyleSheet(self.نمط_الزر("#95a5a6"))

        تخطيط_الأزرار.addStretch()
        تخطيط_الأزرار.addWidget(زر_حفظ)
        تخطيط_الأزرار.addWidget(زر_إلغاء)

        تخطيط_التفاصيل.addLayout(تخطيط_الأزرار)
        تخطيط_التفاصيل.addStretch()

    def نمط_الزر(self, لون_أساسي):
        """
        نمط موحد للأزرار
        """
        return f"""
            QPushButton {{
                background-color: {لون_أساسي};
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background-color: {لون_أساسي}dd;
            }}
            QPushButton:pressed {{
                background-color: {لون_أساسي}aa;
            }}
        """

    def نمط_الحقل(self):
        """
        نمط موحد للحقول النصية
        """
        return """
            QLineEdit, QDoubleSpinBox, QDateEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 12px;
                background-color: #ffffff;
            }
            QLineEdit:focus, QDoubleSpinBox:focus, QDateEdit:focus {
                border-color: #f39c12;
                background-color: #f8f9fa;
            }
        """

    def نمط_القائمة(self):
        """
        نمط موحد للقوائم المنسدلة
        """
        return """
            QComboBox {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 12px;
                background-color: #ffffff;
                min-width: 150px;
            }
            QComboBox:focus {
                border-color: #f39c12;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: url(icons/arrow_down.png);
                width: 12px;
                height: 12px;
            }
        """

    def تحميل_البيانات(self):
        """
        تحميل بيانات المصروفات من قاعدة البيانات
        """
        try:
            استعلام = """
                SELECT م.رقم_المصروف, م.نوع_المصروف, م.المبلغ, م.تاريخ_المصروف,
                       موظ.الاسم_الكامل, م.الوصف, م.ملاحظات, م.رقم_الموظف
                FROM المصروفات م
                LEFT JOIN الموظفين موظ ON م.رقم_الموظف = موظ.رقم_الموظف
                ORDER BY م.تاريخ_المصروف DESC, م.تاريخ_الإنشاء DESC
            """
            self.قاعدة_البيانات.cursor.execute(استعلام)
            المصروفات = self.قاعدة_البيانات.cursor.fetchall()

            # مسح الجدول
            self.جدول_المصروفات.setRowCount(0)

            إجمالي_المصروفات = 0.0

            # إضافة البيانات للجدول
            for صف, مصروف in enumerate(المصروفات):
                self.جدول_المصروفات.insertRow(صف)

                # رقم المصروف
                self.جدول_المصروفات.setItem(صف, 0, QTableWidgetItem(str(مصروف[0])))

                # نوع المصروف
                self.جدول_المصروفات.setItem(صف, 1, QTableWidgetItem(مصروف[1]))

                # المبلغ
                مبلغ = f"{مصروف[2]:,.2f} ريال"
                عنصر_المبلغ = QTableWidgetItem(مبلغ)
                عنصر_المبلغ.setForeground(QBrush(QColor("#e74c3c")))
                self.جدول_المصروفات.setItem(صف, 2, عنصر_المبلغ)
                إجمالي_المصروفات += float(مصروف[2])

                # تاريخ المصروف
                تاريخ = مصروف[3].strftime("%Y-%m-%d") if مصروف[3] else "غير محدد"
                self.جدول_المصروفات.setItem(صف, 3, QTableWidgetItem(تاريخ))

                # الموظف
                موظف = مصروف[4] if مصروف[4] else "غير محدد"
                self.جدول_المصروفات.setItem(صف, 4, QTableWidgetItem(موظف))

                # الوصف
                وصف = مصروف[5][:50] + "..." if مصروف[5] and len(مصروف[5]) > 50 else (مصروف[5] or "")
                self.جدول_المصروفات.setItem(صف, 5, QTableWidgetItem(وصف))

                # حفظ رقم المصروف في البيانات المخفية
                self.جدول_المصروفات.item(صف, 0).setData(Qt.UserRole, مصروف[0])

            # تحديث إجمالي المصروفات
            self.تسمية_الإجمالي.setText(f"الإجمالي: {إجمالي_المصروفات:,.2f} ريال")

            # تعديل عرض الأعمدة
            self.جدول_المصروفات.resizeColumnsToContents()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل البيانات: {str(e)}")

    def تحميل_الموظفين(self):
        """
        تحميل قائمة الموظفين النشطين
        """
        try:
            # مسح القائمة
            self.قائمة_الموظف.clear()
            self.قائمة_الموظف.addItem("-- اختر الموظف --", None)

            # تحميل الموظفين النشطين
            استعلام = """
                SELECT رقم_الموظف, الاسم_الكامل
                FROM الموظفين
                WHERE حالة_النشاط = TRUE
                ORDER BY الاسم_الكامل
            """
            self.قاعدة_البيانات.cursor.execute(استعلام)
            الموظفين = self.قاعدة_البيانات.cursor.fetchall()

            for موظف in الموظفين:
                self.قائمة_الموظف.addItem(موظف[1], موظف[0])

        except Exception as e:
            print(f"خطأ في تحميل الموظفين: {str(e)}")

    def عند_تحديد_مصروف(self):
        """
        عند تحديد مصروف من الجدول
        """
        الصف_المحدد = self.جدول_المصروفات.currentRow()
        if الصف_المحدد >= 0:
            رقم_المصروف = self.جدول_المصروفات.item(الصف_المحدد, 0).data(Qt.UserRole)
            if رقم_المصروف:
                self.المصروف_المحدد = رقم_المصروف
                self.تحميل_تفاصيل_المصروف(رقم_المصروف)
            else:
                self.مسح_النموذج()

    def تحميل_تفاصيل_المصروف(self, رقم_المصروف):
        """
        تحميل تفاصيل المصروف المحدد
        """
        try:
            استعلام = """
                SELECT نوع_المصروف, الوصف, المبلغ, تاريخ_المصروف, رقم_الموظف, ملاحظات
                FROM المصروفات
                WHERE رقم_المصروف = %s
            """
            self.قاعدة_البيانات.cursor.execute(استعلام, (رقم_المصروف,))
            البيانات = self.قاعدة_البيانات.cursor.fetchone()

            if البيانات:
                self.حقل_نوع_المصروف.setText(البيانات[0])
                self.حقل_الوصف.setPlainText(البيانات[1] or "")
                self.حقل_المبلغ.setValue(float(البيانات[2]) if البيانات[2] else 0.0)

                if البيانات[3]:
                    self.حقل_تاريخ_المصروف.setDate(QDate.fromString(البيانات[3].strftime("%Y-%m-%d"), "yyyy-MM-dd"))

                # تحديد الموظف
                if البيانات[4]:
                    for i in range(self.قائمة_الموظف.count()):
                        if self.قائمة_الموظف.itemData(i) == البيانات[4]:
                            self.قائمة_الموظف.setCurrentIndex(i)
                            break
                else:
                    self.قائمة_الموظف.setCurrentIndex(0)

                self.حقل_الملاحظات.setPlainText(البيانات[5] or "")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل تفاصيل المصروف: {str(e)}")

    def مسح_النموذج(self):
        """
        مسح جميع حقول النموذج
        """
        self.حقل_نوع_المصروف.clear()
        self.حقل_الوصف.clear()
        self.حقل_المبلغ.setValue(0.0)
        self.حقل_تاريخ_المصروف.setDate(QDate.currentDate())
        self.قائمة_الموظف.setCurrentIndex(0)
        self.حقل_الملاحظات.clear()
        self.المصروف_المحدد = None

    def إضافة_مصروف(self):
        """
        إضافة مصروف جديد
        """
        نافذة_إضافة = نافذة_مصروف(self, "إضافة")
        if نافذة_إضافة.exec_() == QDialog.Accepted:
            self.تحميل_البيانات()

    def تعديل_المصروف(self):
        """
        تعديل المصروف المحدد
        """
        if not self.المصروف_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد مصروف للتعديل")
            return

        نافذة_تعديل = نافذة_مصروف(self, "تعديل", self.المصروف_المحدد)
        if نافذة_تعديل.exec_() == QDialog.Accepted:
            self.تحميل_البيانات()

    def حذف_المصروف(self):
        """
        حذف المصروف المحدد
        """
        if not self.المصروف_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد مصروف للحذف")
            return

        # تأكيد الحذف
        رد = QMessageBox.question(self, "تأكيد الحذف",
                                 "هل أنت متأكد من حذف هذا المصروف؟\n"
                                 "هذا الإجراء لا يمكن التراجع عنه.",
                                 QMessageBox.Yes | QMessageBox.No)

        if رد == QMessageBox.Yes:
            try:
                استعلام = "DELETE FROM المصروفات WHERE رقم_المصروف = %s"
                self.قاعدة_البيانات.cursor.execute(استعلام, (self.المصروف_المحدد,))
                self.قاعدة_البيانات.connection.commit()

                QMessageBox.information(self, "نجح", "تم حذف المصروف بنجاح")
                self.تحديث_البيانات()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف المصروف: {str(e)}")

    def حفظ_المصروف(self):
        """
        حفظ بيانات المصروف (إضافة أو تعديل)
        """
        # التحقق من صحة البيانات
        if not self.التحقق_من_البيانات():
            return

        # جمع البيانات
        نوع_المصروف = self.حقل_نوع_المصروف.text().strip()
        الوصف = self.حقل_الوصف.toPlainText().strip()
        المبلغ = self.حقل_المبلغ.value()
        تاريخ_المصروف = self.حقل_تاريخ_المصروف.date().toPython()
        رقم_الموظف = self.قائمة_الموظف.currentData()
        الملاحظات = self.حقل_الملاحظات.toPlainText().strip()

        # تحويل القيم الفارغة إلى None
        الوصف = الوصف if الوصف else None
        الملاحظات = الملاحظات if الملاحظات else None

        try:
            if self.المصروف_المحدد:
                # تعديل مصروف موجود
                استعلام = """
                    UPDATE المصروفات
                    SET نوع_المصروف = %s, الوصف = %s, المبلغ = %s,
                        تاريخ_المصروف = %s, رقم_الموظف = %s, ملاحظات = %s
                    WHERE رقم_المصروف = %s
                """
                self.قاعدة_البيانات.cursor.execute(استعلام,
                    (نوع_المصروف, الوصف, المبلغ, تاريخ_المصروف,
                     رقم_الموظف, الملاحظات, self.المصروف_المحدد))
                رسالة = "تم تعديل المصروف بنجاح"
            else:
                # إضافة مصروف جديد
                استعلام = """
                    INSERT INTO المصروفات
                    (نوع_المصروف, الوصف, المبلغ, تاريخ_المصروف, رقم_الموظف, ملاحظات)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """
                self.قاعدة_البيانات.cursor.execute(استعلام,
                    (نوع_المصروف, الوصف, المبلغ, تاريخ_المصروف, رقم_الموظف, الملاحظات))
                رسالة = "تم إضافة المصروف بنجاح"

            self.قاعدة_البيانات.connection.commit()
            QMessageBox.information(self, "نجح", رسالة)
            self.تحديث_البيانات()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ المصروف: {str(e)}")

    def التحقق_من_البيانات(self):
        """
        التحقق من صحة البيانات المدخلة
        """
        if not self.حقل_نوع_المصروف.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال نوع المصروف")
            self.حقل_نوع_المصروف.setFocus()
            return False

        if self.حقل_المبلغ.value() <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ صحيح أكبر من صفر")
            self.حقل_المبلغ.setFocus()
            return False

        return True

    def إلغاء_التعديل(self):
        """
        إلغاء التعديل والعودة للحالة السابقة
        """
        if self.المصروف_المحدد:
            self.تحميل_تفاصيل_المصروف(self.المصروف_المحدد)
        else:
            self.مسح_النموذج()

    def تحديث_البيانات(self):
        """
        تحديث جميع البيانات
        """
        self.تحميل_البيانات()
        self.مسح_النموذج()

    def بحث_في_المصروفات(self):
        """
        البحث في المصروفات
        """
        نص_البحث = self.حقل_البحث.text().strip()

        if not نص_البحث:
            self.تحميل_البيانات()
            return

        try:
            استعلام = """
                SELECT م.رقم_المصروف, م.نوع_المصروف, م.المبلغ, م.تاريخ_المصروف,
                       موظ.الاسم_الكامل, م.الوصف, م.ملاحظات, م.رقم_الموظف
                FROM المصروفات م
                LEFT JOIN الموظفين موظ ON م.رقم_الموظف = موظ.رقم_الموظف
                WHERE م.نوع_المصروف LIKE %s OR م.الوصف LIKE %s OR موظ.الاسم_الكامل LIKE %s
                ORDER BY م.تاريخ_المصروف DESC, م.تاريخ_الإنشاء DESC
            """
            نمط_البحث = f"%{نص_البحث}%"
            self.قاعدة_البيانات.cursor.execute(استعلام, (نمط_البحث, نمط_البحث, نمط_البحث))
            المصروفات = self.قاعدة_البيانات.cursor.fetchall()

            # مسح الجدول
            self.جدول_المصروفات.setRowCount(0)

            إجمالي_المصروفات = 0.0

            # إضافة نتائج البحث
            for صف, مصروف in enumerate(المصروفات):
                self.جدول_المصروفات.insertRow(صف)

                # إضافة البيانات (نفس منطق تحميل_البيانات)
                self.جدول_المصروفات.setItem(صف, 0, QTableWidgetItem(str(مصروف[0])))
                self.جدول_المصروفات.setItem(صف, 1, QTableWidgetItem(مصروف[1]))

                مبلغ = f"{مصروف[2]:,.2f} ريال"
                عنصر_المبلغ = QTableWidgetItem(مبلغ)
                عنصر_المبلغ.setForeground(QBrush(QColor("#e74c3c")))
                self.جدول_المصروفات.setItem(صف, 2, عنصر_المبلغ)
                إجمالي_المصروفات += float(مصروف[2])

                تاريخ = مصروف[3].strftime("%Y-%m-%d") if مصروف[3] else "غير محدد"
                self.جدول_المصروفات.setItem(صف, 3, QTableWidgetItem(تاريخ))

                موظف = مصروف[4] if مصروف[4] else "غير محدد"
                self.جدول_المصروفات.setItem(صف, 4, QTableWidgetItem(موظف))

                وصف = مصروف[5][:50] + "..." if مصروف[5] and len(مصروف[5]) > 50 else (مصروف[5] or "")
                self.جدول_المصروفات.setItem(صف, 5, QTableWidgetItem(وصف))

                self.جدول_المصروفات.item(صف, 0).setData(Qt.UserRole, مصروف[0])

            # تحديث إجمالي المصروفات
            self.تسمية_الإجمالي.setText(f"الإجمالي: {إجمالي_المصروفات:,.2f} ريال")
            self.جدول_المصروفات.resizeColumnsToContents()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في البحث: {str(e)}")

    def فلترة_حسب_التاريخ(self):
        """
        فلترة المصروفات حسب التاريخ
        """
        try:
            تاريخ_من = self.تاريخ_من.date().toPython()
            تاريخ_إلى = self.تاريخ_إلى.date().toPython()

            استعلام = """
                SELECT م.رقم_المصروف, م.نوع_المصروف, م.المبلغ, م.تاريخ_المصروف,
                       موظ.الاسم_الكامل, م.الوصف, م.ملاحظات, م.رقم_الموظف
                FROM المصروفات م
                LEFT JOIN الموظفين موظ ON م.رقم_الموظف = موظ.رقم_الموظف
                WHERE م.تاريخ_المصروف BETWEEN %s AND %s
                ORDER BY م.تاريخ_المصروف DESC, م.تاريخ_الإنشاء DESC
            """
            self.قاعدة_البيانات.cursor.execute(استعلام, (تاريخ_من, تاريخ_إلى))
            المصروفات = self.قاعدة_البيانات.cursor.fetchall()

            # مسح الجدول
            self.جدول_المصروفات.setRowCount(0)

            إجمالي_المصروفات = 0.0

            # إضافة البيانات المفلترة
            for صف, مصروف in enumerate(المصروفات):
                self.جدول_المصروفات.insertRow(صف)

                self.جدول_المصروفات.setItem(صف, 0, QTableWidgetItem(str(مصروف[0])))
                self.جدول_المصروفات.setItem(صف, 1, QTableWidgetItem(مصروف[1]))

                مبلغ = f"{مصروف[2]:,.2f} ريال"
                عنصر_المبلغ = QTableWidgetItem(مبلغ)
                عنصر_المبلغ.setForeground(QBrush(QColor("#e74c3c")))
                self.جدول_المصروفات.setItem(صف, 2, عنصر_المبلغ)
                إجمالي_المصروفات += float(مصروف[2])

                تاريخ = مصروف[3].strftime("%Y-%m-%d") if مصروف[3] else "غير محدد"
                self.جدول_المصروفات.setItem(صف, 3, QTableWidgetItem(تاريخ))

                موظف = مصروف[4] if مصروف[4] else "غير محدد"
                self.جدول_المصروفات.setItem(صف, 4, QTableWidgetItem(موظف))

                وصف = مصروف[5][:50] + "..." if مصروف[5] and len(مصروف[5]) > 50 else (مصروف[5] or "")
                self.جدول_المصروفات.setItem(صف, 5, QTableWidgetItem(وصف))

                self.جدول_المصروفات.item(صف, 0).setData(Qt.UserRole, مصروف[0])

            # تحديث إجمالي المصروفات
            self.تسمية_الإجمالي.setText(f"الإجمالي: {إجمالي_المصروفات:,.2f} ريال")
            self.جدول_المصروفات.resizeColumnsToContents()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في الفلترة: {str(e)}")

    def تقرير_المصروفات(self):
        """
        إنشاء تقرير المصروفات
        """
        QMessageBox.information(self, "تقرير المصروفات", "سيتم تطوير تقرير المصروفات قريباً")


class نافذة_موظف(QDialog):
    """
    نافذة إضافة أو تعديل موظف
    """

    def __init__(self, parent, نوع_العملية, رقم_الموظف=None):
        """
        دالة التهيئة لنافذة الموظف
        """
        super().__init__(parent)
        self.قاعدة_البيانات = parent.قاعدة_البيانات
        self.نوع_العملية = نوع_العملية
        self.رقم_الموظف = رقم_الموظف

        self.إعداد_النافذة()
        self.إنشاء_النموذج()

        if نوع_العملية == "تعديل" and رقم_الموظف:
            self.تحميل_بيانات_الموظف()

    def إعداد_النافذة(self):
        """
        دالة إعداد النافذة
        """
        self.setWindowTitle(f"{self.نوع_العملية} موظف")
        self.setFixedSize(600, 700)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)

    def إنشاء_النموذج(self):
        """
        دالة إنشاء نموذج الموظف
        """
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(20, 20, 20, 20)
        التخطيط_الرئيسي.setSpacing(15)

        # عنوان النافذة
        عنوان = QLabel(f"{self.نوع_العملية} موظف")
        عنوان.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            padding: 10px;
            background-color: #ecf0f1;
            border-radius: 5px;
        """)
        التخطيط_الرئيسي.addWidget(عنوان)

        # منطقة التمرير
        منطقة_التمرير = QScrollArea()
        منطقة_التمرير.setWidgetResizable(True)
        منطقة_التمرير.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        widget_المحتوى = QWidget()
        تخطيط_المحتوى = QVBoxLayout(widget_المحتوى)
        تخطيط_المحتوى.setSpacing(15)

        # مجموعة البيانات الشخصية
        مجموعة_شخصية = QGroupBox("البيانات الشخصية")
        مجموعة_شخصية.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2c3e50;
            }
        """)

        تخطيط_شخصي = QFormLayout(مجموعة_شخصية)
        تخطيط_شخصي.setSpacing(10)

        # حقول البيانات الشخصية
        self.حقل_الاسم_الكامل = QLineEdit()
        self.حقل_الاسم_الكامل.setPlaceholderText("أدخل الاسم الكامل")
        self.حقل_الاسم_الكامل.setStyleSheet(self.نمط_الحقل())

        self.حقل_المنصب = QLineEdit()
        self.حقل_المنصب.setPlaceholderText("أدخل المنصب")
        self.حقل_المنصب.setStyleSheet(self.نمط_الحقل())

        self.حقل_رقم_الهاتف = QLineEdit()
        self.حقل_رقم_الهاتف.setPlaceholderText("أدخل رقم الهاتف")
        self.حقل_رقم_الهاتف.setStyleSheet(self.نمط_الحقل())

        self.حقل_العنوان = QTextEdit()
        self.حقل_العنوان.setPlaceholderText("أدخل العنوان")
        self.حقل_العنوان.setMaximumHeight(60)
        self.حقل_العنوان.setStyleSheet("""
            QTextEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 12px;
                background-color: #ffffff;
            }
            QTextEdit:focus {
                border-color: #9b59b6;
            }
        """)

        self.حقل_تاريخ_التوظيف = QDateEdit()
        self.حقل_تاريخ_التوظيف.setDate(QDate.currentDate())
        self.حقل_تاريخ_التوظيف.setCalendarPopup(True)
        self.حقل_تاريخ_التوظيف.setStyleSheet(self.نمط_الحقل())

        تخطيط_شخصي.addRow("الاسم الكامل:", self.حقل_الاسم_الكامل)
        تخطيط_شخصي.addRow("المنصب:", self.حقل_المنصب)
        تخطيط_شخصي.addRow("رقم الهاتف:", self.حقل_رقم_الهاتف)
        تخطيط_شخصي.addRow("العنوان:", self.حقل_العنوان)
        تخطيط_شخصي.addRow("تاريخ التوظيف:", self.حقل_تاريخ_التوظيف)

        تخطيط_المحتوى.addWidget(مجموعة_شخصية)

        # مجموعة البيانات المالية
        مجموعة_مالية = QGroupBox("البيانات المالية")
        مجموعة_مالية.setStyleSheet(مجموعة_شخصية.styleSheet())

        تخطيط_مالي = QFormLayout(مجموعة_مالية)
        تخطيط_مالي.setSpacing(10)

        self.حقل_الراتب_الأساسي = QDoubleSpinBox()
        self.حقل_الراتب_الأساسي.setRange(0, 999999.99)
        self.حقل_الراتب_الأساسي.setDecimals(2)
        self.حقل_الراتب_الأساسي.setSuffix(" ريال")
        self.حقل_الراتب_الأساسي.setStyleSheet(self.نمط_الحقل())

        self.حقل_النسبة = QDoubleSpinBox()
        self.حقل_النسبة.setRange(0, 100)
        self.حقل_النسبة.setDecimals(2)
        self.حقل_النسبة.setSuffix(" %")
        self.حقل_النسبة.setStyleSheet(self.نمط_الحقل())

        self.حقل_الرصيد_الحالي = QDoubleSpinBox()
        self.حقل_الرصيد_الحالي.setRange(-999999.99, 999999.99)
        self.حقل_الرصيد_الحالي.setDecimals(2)
        self.حقل_الرصيد_الحالي.setSuffix(" ريال")
        self.حقل_الرصيد_الحالي.setStyleSheet(self.نمط_الحقل())

        تخطيط_مالي.addRow("الراتب الأساسي:", self.حقل_الراتب_الأساسي)
        تخطيط_مالي.addRow("النسبة:", self.حقل_النسبة)
        تخطيط_مالي.addRow("الرصيد الحالي:", self.حقل_الرصيد_الحالي)

        تخطيط_المحتوى.addWidget(مجموعة_مالية)

        # مجموعة بيانات النظام
        مجموعة_نظام = QGroupBox("بيانات النظام")
        مجموعة_نظام.setStyleSheet(مجموعة_شخصية.styleSheet())

        تخطيط_نظام = QFormLayout(مجموعة_نظام)
        تخطيط_نظام.setSpacing(10)

        self.حقل_اسم_المستخدم = QLineEdit()
        self.حقل_اسم_المستخدم.setPlaceholderText("أدخل اسم المستخدم")
        self.حقل_اسم_المستخدم.setStyleSheet(self.نمط_الحقل())

        self.حقل_كلمة_المرور = QLineEdit()
        self.حقل_كلمة_المرور.setPlaceholderText("أدخل كلمة المرور")
        self.حقل_كلمة_المرور.setEchoMode(QLineEdit.Password)
        self.حقل_كلمة_المرور.setStyleSheet(self.نمط_الحقل())

        self.حقل_الصلاحيات = QTextEdit()
        self.حقل_الصلاحيات.setPlaceholderText("أدخل الصلاحيات")
        self.حقل_الصلاحيات.setMaximumHeight(60)
        self.حقل_الصلاحيات.setStyleSheet(self.حقل_العنوان.styleSheet())

        self.مربع_الحالة = QCheckBox("الموظف نشط")
        self.مربع_الحالة.setChecked(True)
        self.مربع_الحالة.setStyleSheet("""
            QCheckBox {
                font-size: 12px;
                color: #2c3e50;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QCheckBox::indicator:checked {
                background-color: #9b59b6;
                border: 2px solid #9b59b6;
            }
        """)

        تخطيط_نظام.addRow("اسم المستخدم:", self.حقل_اسم_المستخدم)
        تخطيط_نظام.addRow("كلمة المرور:", self.حقل_كلمة_المرور)
        تخطيط_نظام.addRow("الصلاحيات:", self.حقل_الصلاحيات)
        تخطيط_نظام.addRow("", self.مربع_الحالة)

        تخطيط_المحتوى.addWidget(مجموعة_نظام)

        منطقة_التمرير.setWidget(widget_المحتوى)
        التخطيط_الرئيسي.addWidget(منطقة_التمرير)

        # أزرار الحفظ والإلغاء
        تخطيط_الأزرار = QHBoxLayout()

        زر_حفظ = QPushButton("حفظ")
        زر_حفظ.setIcon(QIcon("icons/save.png"))
        زر_حفظ.clicked.connect(self.حفظ_الموظف)
        زر_حفظ.setStyleSheet(self.نمط_الزر("#27ae60"))

        زر_إلغاء = QPushButton("إلغاء")
        زر_إلغاء.setIcon(QIcon("icons/cancel.png"))
        زر_إلغاء.clicked.connect(self.reject)
        زر_إلغاء.setStyleSheet(self.نمط_الزر("#95a5a6"))

        تخطيط_الأزرار.addStretch()
        تخطيط_الأزرار.addWidget(زر_حفظ)
        تخطيط_الأزرار.addWidget(زر_إلغاء)

        التخطيط_الرئيسي.addLayout(تخطيط_الأزرار)

    def نمط_الزر(self, لون_أساسي):
        """
        نمط موحد للأزرار
        """
        return f"""
            QPushButton {{
                background-color: {لون_أساسي};
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background-color: {لون_أساسي}dd;
            }}
            QPushButton:pressed {{
                background-color: {لون_أساسي}aa;
            }}
        """

    def نمط_الحقل(self):
        """
        نمط موحد للحقول النصية
        """
        return """
            QLineEdit, QDoubleSpinBox, QDateEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 12px;
                background-color: #ffffff;
            }
            QLineEdit:focus, QDoubleSpinBox:focus, QDateEdit:focus {
                border-color: #9b59b6;
                background-color: #f8f9fa;
            }
        """

    def تحميل_بيانات_الموظف(self):
        """
        تحميل بيانات الموظف للتعديل
        """
        try:
            استعلام = """
                SELECT الاسم_الكامل, المنصب, رقم_الهاتف, العنوان, الراتب_الأساسي,
                       النسبة, الرصيد_الحالي, اسم_المستخدم, كلمة_المرور, الصلاحيات,
                       حالة_النشاط, تاريخ_التوظيف
                FROM الموظفين
                WHERE رقم_الموظف = %s
            """
            self.قاعدة_البيانات.cursor.execute(استعلام, (self.رقم_الموظف,))
            البيانات = self.قاعدة_البيانات.cursor.fetchone()

            if البيانات:
                self.حقل_الاسم_الكامل.setText(البيانات[0])
                self.حقل_المنصب.setText(البيانات[1] or "")
                self.حقل_رقم_الهاتف.setText(البيانات[2] or "")
                self.حقل_العنوان.setPlainText(البيانات[3] or "")
                self.حقل_الراتب_الأساسي.setValue(float(البيانات[4]) if البيانات[4] else 0.0)
                self.حقل_النسبة.setValue(float(البيانات[5]) if البيانات[5] else 0.0)
                self.حقل_الرصيد_الحالي.setValue(float(البيانات[6]) if البيانات[6] else 0.0)
                self.حقل_اسم_المستخدم.setText(البيانات[7] or "")
                self.حقل_كلمة_المرور.setText(البيانات[8] or "")
                self.حقل_الصلاحيات.setPlainText(البيانات[9] or "")
                self.مربع_الحالة.setChecked(البيانات[10])

                if البيانات[11]:
                    self.حقل_تاريخ_التوظيف.setDate(QDate.fromString(البيانات[11].strftime("%Y-%m-%d"), "yyyy-MM-dd"))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات الموظف: {str(e)}")

    def حفظ_الموظف(self):
        """
        حفظ بيانات الموظف
        """
        # التحقق من صحة البيانات
        if not self.التحقق_من_البيانات():
            return

        # جمع البيانات
        الاسم_الكامل = self.حقل_الاسم_الكامل.text().strip()
        المنصب = self.حقل_المنصب.text().strip()
        رقم_الهاتف = self.حقل_رقم_الهاتف.text().strip()
        العنوان = self.حقل_العنوان.toPlainText().strip()
        الراتب_الأساسي = self.حقل_الراتب_الأساسي.value()
        النسبة = self.حقل_النسبة.value()
        الرصيد_الحالي = self.حقل_الرصيد_الحالي.value()
        اسم_المستخدم = self.حقل_اسم_المستخدم.text().strip()
        كلمة_المرور = self.حقل_كلمة_المرور.text().strip()
        الصلاحيات = self.حقل_الصلاحيات.toPlainText().strip()
        حالة_النشاط = self.مربع_الحالة.isChecked()
        تاريخ_التوظيف = self.حقل_تاريخ_التوظيف.date().toPython()

        # تحويل القيم الفارغة إلى None
        المنصب = المنصب if المنصب else None
        رقم_الهاتف = رقم_الهاتف if رقم_الهاتف else None
        العنوان = العنوان if العنوان else None
        اسم_المستخدم = اسم_المستخدم if اسم_المستخدم else None
        كلمة_المرور = كلمة_المرور if كلمة_المرور else None
        الصلاحيات = الصلاحيات if الصلاحيات else None

        try:
            if self.نوع_العملية == "تعديل":
                # تعديل موظف موجود
                استعلام = """
                    UPDATE الموظفين
                    SET الاسم_الكامل = %s, المنصب = %s, رقم_الهاتف = %s, العنوان = %s,
                        الراتب_الأساسي = %s, النسبة = %s, الرصيد_الحالي = %s,
                        اسم_المستخدم = %s, كلمة_المرور = %s, الصلاحيات = %s,
                        حالة_النشاط = %s, تاريخ_التوظيف = %s
                    WHERE رقم_الموظف = %s
                """
                self.قاعدة_البيانات.cursor.execute(استعلام,
                    (الاسم_الكامل, المنصب, رقم_الهاتف, العنوان, الراتب_الأساسي,
                     النسبة, الرصيد_الحالي, اسم_المستخدم, كلمة_المرور, الصلاحيات,
                     حالة_النشاط, تاريخ_التوظيف, self.رقم_الموظف))
                رسالة = "تم تعديل الموظف بنجاح"
            else:
                # إضافة موظف جديد
                استعلام = """
                    INSERT INTO الموظفين
                    (الاسم_الكامل, المنصب, رقم_الهاتف, العنوان, الراتب_الأساسي,
                     النسبة, الرصيد_الحالي, اسم_المستخدم, كلمة_المرور, الصلاحيات,
                     حالة_النشاط, تاريخ_التوظيف)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                self.قاعدة_البيانات.cursor.execute(استعلام,
                    (الاسم_الكامل, المنصب, رقم_الهاتف, العنوان, الراتب_الأساسي,
                     النسبة, الرصيد_الحالي, اسم_المستخدم, كلمة_المرور, الصلاحيات,
                     حالة_النشاط, تاريخ_التوظيف))
                رسالة = "تم إضافة الموظف بنجاح"

            self.قاعدة_البيانات.connection.commit()
            QMessageBox.information(self, "نجح", رسالة)
            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الموظف: {str(e)}")

    def التحقق_من_البيانات(self):
        """
        التحقق من صحة البيانات المدخلة
        """
        if not self.حقل_الاسم_الكامل.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال الاسم الكامل")
            self.حقل_الاسم_الكامل.setFocus()
            return False

        # التحقق من عدم تكرار اسم المستخدم
        اسم_المستخدم = self.حقل_اسم_المستخدم.text().strip()
        if اسم_المستخدم:
            try:
                if self.نوع_العملية == "تعديل":
                    # في حالة التعديل، تجاهل الموظف الحالي
                    استعلام = "SELECT COUNT(*) FROM الموظفين WHERE اسم_المستخدم = %s AND رقم_الموظف != %s"
                    self.قاعدة_البيانات.cursor.execute(استعلام, (اسم_المستخدم, self.رقم_الموظف))
                else:
                    # في حالة الإضافة
                    استعلام = "SELECT COUNT(*) FROM الموظفين WHERE اسم_المستخدم = %s"
                    self.قاعدة_البيانات.cursor.execute(استعلام, (اسم_المستخدم,))

                if self.قاعدة_البيانات.cursor.fetchone()[0] > 0:
                    QMessageBox.warning(self, "تحذير", "اسم المستخدم موجود مسبقاً")
                    self.حقل_اسم_المستخدم.setFocus()
                    return False

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في التحقق من البيانات: {str(e)}")
                return False

        return True


class نافذة_مصروف(QDialog):
    """
    نافذة إضافة أو تعديل مصروف
    """

    def __init__(self, parent, نوع_العملية, رقم_المصروف=None):
        """
        دالة التهيئة لنافذة المصروف
        """
        super().__init__(parent)
        self.قاعدة_البيانات = parent.قاعدة_البيانات
        self.نوع_العملية = نوع_العملية
        self.رقم_المصروف = رقم_المصروف

        self.إعداد_النافذة()
        self.إنشاء_النموذج()
        self.تحميل_الموظفين()

        if نوع_العملية == "تعديل" and رقم_المصروف:
            self.تحميل_بيانات_المصروف()

    def إعداد_النافذة(self):
        """
        دالة إعداد النافذة
        """
        self.setWindowTitle(f"{self.نوع_العملية} مصروف")
        self.setFixedSize(500, 600)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)

    def إنشاء_النموذج(self):
        """
        دالة إنشاء نموذج المصروف
        """
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(20, 20, 20, 20)
        التخطيط_الرئيسي.setSpacing(15)

        # عنوان النافذة
        عنوان = QLabel(f"{self.نوع_العملية} مصروف")
        عنوان.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            padding: 10px;
            background-color: #ecf0f1;
            border-radius: 5px;
        """)
        التخطيط_الرئيسي.addWidget(عنوان)

        # مجموعة البيانات الأساسية
        مجموعة_أساسية = QGroupBox("بيانات المصروف")
        مجموعة_أساسية.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2c3e50;
            }
        """)

        تخطيط_أساسي = QFormLayout(مجموعة_أساسية)
        تخطيط_أساسي.setSpacing(10)

        # حقول البيانات
        self.حقل_نوع_المصروف = QLineEdit()
        self.حقل_نوع_المصروف.setPlaceholderText("أدخل نوع المصروف")
        self.حقل_نوع_المصروف.setStyleSheet(self.نمط_الحقل())

        self.حقل_المبلغ = QDoubleSpinBox()
        self.حقل_المبلغ.setRange(0, 999999.99)
        self.حقل_المبلغ.setDecimals(2)
        self.حقل_المبلغ.setSuffix(" ريال")
        self.حقل_المبلغ.setStyleSheet(self.نمط_الحقل())

        self.حقل_تاريخ_المصروف = QDateEdit()
        self.حقل_تاريخ_المصروف.setDate(QDate.currentDate())
        self.حقل_تاريخ_المصروف.setCalendarPopup(True)
        self.حقل_تاريخ_المصروف.setStyleSheet(self.نمط_الحقل())

        self.قائمة_الموظف = QComboBox()
        self.قائمة_الموظف.addItem("-- اختر الموظف --", None)
        self.قائمة_الموظف.setStyleSheet(self.نمط_القائمة())

        self.حقل_الوصف = QTextEdit()
        self.حقل_الوصف.setPlaceholderText("أدخل وصف المصروف")
        self.حقل_الوصف.setMaximumHeight(80)
        self.حقل_الوصف.setStyleSheet("""
            QTextEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 12px;
                background-color: #ffffff;
            }
            QTextEdit:focus {
                border-color: #f39c12;
            }
        """)

        self.حقل_الملاحظات = QTextEdit()
        self.حقل_الملاحظات.setPlaceholderText("أدخل ملاحظات إضافية")
        self.حقل_الملاحظات.setMaximumHeight(80)
        self.حقل_الملاحظات.setStyleSheet(self.حقل_الوصف.styleSheet())

        تخطيط_أساسي.addRow("نوع المصروف:", self.حقل_نوع_المصروف)
        تخطيط_أساسي.addRow("المبلغ:", self.حقل_المبلغ)
        تخطيط_أساسي.addRow("تاريخ المصروف:", self.حقل_تاريخ_المصروف)
        تخطيط_أساسي.addRow("الموظف:", self.قائمة_الموظف)
        تخطيط_أساسي.addRow("الوصف:", self.حقل_الوصف)
        تخطيط_أساسي.addRow("الملاحظات:", self.حقل_الملاحظات)

        التخطيط_الرئيسي.addWidget(مجموعة_أساسية)

        # أزرار الحفظ والإلغاء
        تخطيط_الأزرار = QHBoxLayout()

        زر_حفظ = QPushButton("حفظ")
        زر_حفظ.setIcon(QIcon("icons/save.png"))
        زر_حفظ.clicked.connect(self.حفظ_المصروف)
        زر_حفظ.setStyleSheet(self.نمط_الزر("#27ae60"))

        زر_إلغاء = QPushButton("إلغاء")
        زر_إلغاء.setIcon(QIcon("icons/cancel.png"))
        زر_إلغاء.clicked.connect(self.reject)
        زر_إلغاء.setStyleSheet(self.نمط_الزر("#95a5a6"))

        تخطيط_الأزرار.addStretch()
        تخطيط_الأزرار.addWidget(زر_حفظ)
        تخطيط_الأزرار.addWidget(زر_إلغاء)

        التخطيط_الرئيسي.addLayout(تخطيط_الأزرار)

    def نمط_الزر(self, لون_أساسي):
        """
        نمط موحد للأزرار
        """
        return f"""
            QPushButton {{
                background-color: {لون_أساسي};
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background-color: {لون_أساسي}dd;
            }}
            QPushButton:pressed {{
                background-color: {لون_أساسي}aa;
            }}
        """

    def نمط_الحقل(self):
        """
        نمط موحد للحقول النصية
        """
        return """
            QLineEdit, QDoubleSpinBox, QDateEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 12px;
                background-color: #ffffff;
            }
            QLineEdit:focus, QDoubleSpinBox:focus, QDateEdit:focus {
                border-color: #f39c12;
                background-color: #f8f9fa;
            }
        """

    def نمط_القائمة(self):
        """
        نمط موحد للقوائم المنسدلة
        """
        return """
            QComboBox {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 12px;
                background-color: #ffffff;
            }
            QComboBox:focus {
                border-color: #f39c12;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: url(icons/arrow_down.png);
                width: 12px;
                height: 12px;
            }
        """

    def تحميل_الموظفين(self):
        """
        تحميل قائمة الموظفين
        """
        try:
            استعلام = "SELECT رقم_الموظف, الاسم_الكامل FROM الموظفين WHERE حالة_النشاط = 1 ORDER BY الاسم_الكامل"
            self.قاعدة_البيانات.cursor.execute(استعلام)
            الموظفين = self.قاعدة_البيانات.cursor.fetchall()

            for موظف in الموظفين:
                self.قائمة_الموظف.addItem(موظف[1], موظف[0])

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل الموظفين: {str(e)}")

    def تحميل_بيانات_المصروف(self):
        """
        تحميل بيانات المصروف للتعديل
        """
        try:
            استعلام = """
                SELECT نوع_المصروف, المبلغ, تاريخ_المصروف, رقم_الموظف, الوصف, ملاحظات
                FROM المصروفات
                WHERE رقم_المصروف = %s
            """
            self.قاعدة_البيانات.cursor.execute(استعلام, (self.رقم_المصروف,))
            البيانات = self.قاعدة_البيانات.cursor.fetchone()

            if البيانات:
                self.حقل_نوع_المصروف.setText(البيانات[0])
                self.حقل_المبلغ.setValue(float(البيانات[1]))

                if البيانات[2]:
                    self.حقل_تاريخ_المصروف.setDate(QDate.fromString(البيانات[2].strftime("%Y-%m-%d"), "yyyy-MM-dd"))

                # تحديد الموظف
                if البيانات[3]:
                    فهرس = self.قائمة_الموظف.findData(البيانات[3])
                    if فهرس >= 0:
                        self.قائمة_الموظف.setCurrentIndex(فهرس)

                self.حقل_الوصف.setPlainText(البيانات[4] or "")
                self.حقل_الملاحظات.setPlainText(البيانات[5] or "")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات المصروف: {str(e)}")

    def حفظ_المصروف(self):
        """
        حفظ بيانات المصروف
        """
        # التحقق من صحة البيانات
        if not self.التحقق_من_البيانات():
            return

        # جمع البيانات
        نوع_المصروف = self.حقل_نوع_المصروف.text().strip()
        المبلغ = self.حقل_المبلغ.value()
        تاريخ_المصروف = self.حقل_تاريخ_المصروف.date().toPython()
        رقم_الموظف = self.قائمة_الموظف.currentData()
        الوصف = self.حقل_الوصف.toPlainText().strip()
        الملاحظات = self.حقل_الملاحظات.toPlainText().strip()

        # تحويل القيم الفارغة إلى None
        الوصف = الوصف if الوصف else None
        الملاحظات = الملاحظات if الملاحظات else None

        try:
            if self.نوع_العملية == "تعديل":
                # تعديل مصروف موجود
                استعلام = """
                    UPDATE المصروفات
                    SET نوع_المصروف = %s, المبلغ = %s, تاريخ_المصروف = %s,
                        رقم_الموظف = %s, الوصف = %s, ملاحظات = %s
                    WHERE رقم_المصروف = %s
                """
                self.قاعدة_البيانات.cursor.execute(استعلام,
                    (نوع_المصروف, المبلغ, تاريخ_المصروف, رقم_الموظف,
                     الوصف, الملاحظات, self.رقم_المصروف))
                رسالة = "تم تعديل المصروف بنجاح"
            else:
                # إضافة مصروف جديد
                استعلام = """
                    INSERT INTO المصروفات
                    (نوع_المصروف, المبلغ, تاريخ_المصروف, رقم_الموظف, الوصف, ملاحظات)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """
                self.قاعدة_البيانات.cursor.execute(استعلام,
                    (نوع_المصروف, المبلغ, تاريخ_المصروف, رقم_الموظف, الوصف, الملاحظات))
                رسالة = "تم إضافة المصروف بنجاح"

            self.قاعدة_البيانات.connection.commit()
            QMessageBox.information(self, "نجح", رسالة)
            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ المصروف: {str(e)}")

    def التحقق_من_البيانات(self):
        """
        التحقق من صحة البيانات المدخلة
        """
        if not self.حقل_نوع_المصروف.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال نوع المصروف")
            self.حقل_نوع_المصروف.setFocus()
            return False

        if self.حقل_المبلغ.value() <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ صحيح أكبر من صفر")
            self.حقل_المبلغ.setFocus()
            return False

        return True


class نافذة_بنك(QDialog):
    """
    نافذة إضافة أو تعديل بنك
    """

    def __init__(self, parent, نوع_العملية, رقم_البنك=None):
        """
        دالة التهيئة لنافذة البنك
        """
        super().__init__(parent)
        self.قاعدة_البيانات = parent.قاعدة_البيانات
        self.نوع_العملية = نوع_العملية
        self.رقم_البنك = رقم_البنك

        self.إعداد_النافذة()
        self.إنشاء_النموذج()

        if نوع_العملية == "إضافة فرع":
            self.تحميل_البنوك_الرئيسية()

        if نوع_العملية == "تعديل" and رقم_البنك:
            self.تحميل_بيانات_البنك()

    def إعداد_النافذة(self):
        """
        دالة إعداد النافذة
        """
        self.setWindowTitle(f"{self.نوع_العملية}")
        self.setFixedSize(500, 400)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)

    def إنشاء_النموذج(self):
        """
        دالة إنشاء نموذج البنك
        """
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(20, 20, 20, 20)
        التخطيط_الرئيسي.setSpacing(15)

        # عنوان النافذة
        عنوان = QLabel(f"{self.نوع_العملية}")
        عنوان.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            padding: 10px;
            background-color: #ecf0f1;
            border-radius: 5px;
        """)
        التخطيط_الرئيسي.addWidget(عنوان)

        # مجموعة البيانات الأساسية
        مجموعة_أساسية = QGroupBox("بيانات البنك")
        مجموعة_أساسية.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2c3e50;
            }
        """)

        تخطيط_أساسي = QFormLayout(مجموعة_أساسية)
        تخطيط_أساسي.setSpacing(10)

        # حقول البيانات
        self.حقل_اسم_البنك = QLineEdit()
        self.حقل_اسم_البنك.setPlaceholderText("أدخل اسم البنك")
        self.حقل_اسم_البنك.setStyleSheet(self.نمط_الحقل())

        self.حقل_رقم_حساب_الشركة = QLineEdit()
        self.حقل_رقم_حساب_الشركة.setPlaceholderText("أدخل رقم حساب الشركة")
        self.حقل_رقم_حساب_الشركة.setStyleSheet(self.نمط_الحقل())

        self.حقل_رقم_الهاتف = QLineEdit()
        self.حقل_رقم_الهاتف.setPlaceholderText("أدخل رقم الهاتف")
        self.حقل_رقم_الهاتف.setStyleSheet(self.نمط_الحقل())

        self.حقل_العنوان = QTextEdit()
        self.حقل_العنوان.setPlaceholderText("أدخل العنوان")
        self.حقل_العنوان.setMaximumHeight(80)
        self.حقل_العنوان.setStyleSheet("""
            QTextEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 12px;
                background-color: #ffffff;
            }
            QTextEdit:focus {
                border-color: #3498db;
            }
        """)

        # قائمة البنك الرئيسي (للفروع فقط)
        if self.نوع_العملية == "إضافة فرع":
            self.قائمة_البنك_الرئيسي = QComboBox()
            self.قائمة_البنك_الرئيسي.addItem("-- اختر البنك الرئيسي --", None)
            self.قائمة_البنك_الرئيسي.setStyleSheet(self.نمط_القائمة())

        self.مربع_الحالة = QCheckBox("البنك نشط")
        self.مربع_الحالة.setChecked(True)
        self.مربع_الحالة.setStyleSheet("""
            QCheckBox {
                font-size: 12px;
                color: #2c3e50;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QCheckBox::indicator:checked {
                background-color: #3498db;
                border: 2px solid #3498db;
            }
        """)

        تخطيط_أساسي.addRow("اسم البنك:", self.حقل_اسم_البنك)
        تخطيط_أساسي.addRow("رقم حساب الشركة:", self.حقل_رقم_حساب_الشركة)
        تخطيط_أساسي.addRow("رقم الهاتف:", self.حقل_رقم_الهاتف)
        تخطيط_أساسي.addRow("العنوان:", self.حقل_العنوان)

        if self.نوع_العملية == "إضافة فرع":
            تخطيط_أساسي.addRow("البنك الرئيسي:", self.قائمة_البنك_الرئيسي)

        تخطيط_أساسي.addRow("", self.مربع_الحالة)

        التخطيط_الرئيسي.addWidget(مجموعة_أساسية)

        # أزرار الحفظ والإلغاء
        تخطيط_الأزرار = QHBoxLayout()

        زر_حفظ = QPushButton("حفظ")
        زر_حفظ.setIcon(QIcon("icons/save.png"))
        زر_حفظ.clicked.connect(self.حفظ_البنك)
        زر_حفظ.setStyleSheet(self.نمط_الزر("#27ae60"))

        زر_إلغاء = QPushButton("إلغاء")
        زر_إلغاء.setIcon(QIcon("icons/cancel.png"))
        زر_إلغاء.clicked.connect(self.reject)
        زر_إلغاء.setStyleSheet(self.نمط_الزر("#95a5a6"))

        تخطيط_الأزرار.addStretch()
        تخطيط_الأزرار.addWidget(زر_حفظ)
        تخطيط_الأزرار.addWidget(زر_إلغاء)

        التخطيط_الرئيسي.addLayout(تخطيط_الأزرار)

    def نمط_الزر(self, لون_أساسي):
        """
        نمط موحد للأزرار
        """
        return f"""
            QPushButton {{
                background-color: {لون_أساسي};
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background-color: {لون_أساسي}dd;
            }}
            QPushButton:pressed {{
                background-color: {لون_أساسي}aa;
            }}
        """

    def نمط_الحقل(self):
        """
        نمط موحد للحقول النصية
        """
        return """
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 12px;
                background-color: #ffffff;
            }
            QLineEdit:focus {
                border-color: #3498db;
                background-color: #f8f9fa;
            }
        """

    def نمط_القائمة(self):
        """
        نمط موحد للقوائم المنسدلة
        """
        return """
            QComboBox {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 12px;
                background-color: #ffffff;
            }
            QComboBox:focus {
                border-color: #3498db;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: url(icons/arrow_down.png);
                width: 12px;
                height: 12px;
            }
        """

    def تحميل_البنوك_الرئيسية(self):
        """
        تحميل قائمة البنوك الرئيسية
        """
        try:
            استعلام = "SELECT رقم_البنك, اسم_البنك FROM البنوك WHERE بنك_رئيسي IS NULL ORDER BY اسم_البنك"
            self.قاعدة_البيانات.cursor.execute(استعلام)
            البنوك = self.قاعدة_البيانات.cursor.fetchall()

            for بنك in البنوك:
                self.قائمة_البنك_الرئيسي.addItem(بنك[1], بنك[0])

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل البنوك الرئيسية: {str(e)}")

    def تحميل_بيانات_البنك(self):
        """
        تحميل بيانات البنك للتعديل
        """
        try:
            استعلام = """
                SELECT اسم_البنك, رقم_حساب_الشركة, رقم_الهاتف, العنوان, بنك_رئيسي, حالة_النشاط
                FROM البنوك
                WHERE رقم_البنك = %s
            """
            self.قاعدة_البيانات.cursor.execute(استعلام, (self.رقم_البنك,))
            البيانات = self.قاعدة_البيانات.cursor.fetchone()

            if البيانات:
                self.حقل_اسم_البنك.setText(البيانات[0])
                self.حقل_رقم_حساب_الشركة.setText(البيانات[1] or "")
                self.حقل_رقم_الهاتف.setText(البيانات[2] or "")
                self.حقل_العنوان.setPlainText(البيانات[3] or "")
                self.مربع_الحالة.setChecked(البيانات[5])

                # إذا كان فرع، تحميل البنوك الرئيسية وتحديد البنك الرئيسي
                if البيانات[4]:  # إذا كان له بنك رئيسي
                    self.تحميل_البنوك_الرئيسية()
                    فهرس = self.قائمة_البنك_الرئيسي.findData(البيانات[4])
                    if فهرس >= 0:
                        self.قائمة_البنك_الرئيسي.setCurrentIndex(فهرس)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات البنك: {str(e)}")

    def حفظ_البنك(self):
        """
        حفظ بيانات البنك
        """
        # التحقق من صحة البيانات
        if not self.التحقق_من_البيانات():
            return

        # جمع البيانات
        اسم_البنك = self.حقل_اسم_البنك.text().strip()
        رقم_حساب_الشركة = self.حقل_رقم_حساب_الشركة.text().strip()
        رقم_الهاتف = self.حقل_رقم_الهاتف.text().strip()
        العنوان = self.حقل_العنوان.toPlainText().strip()
        حالة_النشاط = self.مربع_الحالة.isChecked()

        # تحويل القيم الفارغة إلى None
        رقم_حساب_الشركة = رقم_حساب_الشركة if رقم_حساب_الشركة else None
        رقم_الهاتف = رقم_الهاتف if رقم_الهاتف else None
        العنوان = العنوان if العنوان else None

        # البنك الرئيسي (للفروع فقط)
        بنك_رئيسي = None
        if self.نوع_العملية == "إضافة فرع":
            بنك_رئيسي = self.قائمة_البنك_الرئيسي.currentData()

        try:
            if self.نوع_العملية == "تعديل":
                # تعديل بنك موجود
                استعلام = """
                    UPDATE البنوك
                    SET اسم_البنك = %s, رقم_حساب_الشركة = %s, رقم_الهاتف = %s, العنوان = %s, حالة_النشاط = %s
                    WHERE رقم_البنك = %s
                """
                self.قاعدة_البيانات.cursor.execute(استعلام,
                    (اسم_البنك, رقم_حساب_الشركة, رقم_الهاتف, العنوان, حالة_النشاط, self.رقم_البنك))
                رسالة = "تم تعديل البنك بنجاح"
            else:
                # إضافة بنك جديد
                استعلام = """
                    INSERT INTO البنوك
                    (اسم_البنك, رقم_حساب_الشركة, رقم_الهاتف, العنوان, بنك_رئيسي, حالة_النشاط)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """
                self.قاعدة_البيانات.cursor.execute(استعلام,
                    (اسم_البنك, رقم_حساب_الشركة, رقم_الهاتف, العنوان, بنك_رئيسي, حالة_النشاط))
                رسالة = f"تم {self.نوع_العملية} بنجاح"

            self.قاعدة_البيانات.connection.commit()
            QMessageBox.information(self, "نجح", رسالة)
            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ البنك: {str(e)}")

    def التحقق_من_البيانات(self):
        """
        التحقق من صحة البيانات المدخلة
        """
        if not self.حقل_اسم_البنك.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم البنك")
            self.حقل_اسم_البنك.setFocus()
            return False

        # التحقق من اختيار البنك الرئيسي للفروع
        if self.نوع_العملية == "إضافة فرع":
            if not self.قائمة_البنك_الرئيسي.currentData():
                QMessageBox.warning(self, "تحذير", "يرجى اختيار البنك الرئيسي")
                self.قائمة_البنك_الرئيسي.setFocus()
                return False

        return True

class واجهة_البنوك(QWidget):
    """
    واجهة إدارة البنوك مع نظام الشجرة للبنوك والفروع
    """

    def __init__(self):
        """
        دالة التهيئة لواجهة البنوك
        """
        super().__init__()
        self.قاعدة_البيانات = قاعدة_البيانات()
        self.قاعدة_البيانات.اتصال_قاعدة_البيانات()
        self.قاعدة_البيانات.cursor.execute(f"USE {self.قاعدة_البيانات.database}")

        self.البنك_المحدد = None
        self.إعداد_الواجهة()
        self.تحميل_البيانات()

    def إعداد_الواجهة(self):
        """
        دالة إعداد واجهة البنوك
        """
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(20, 20, 20, 20)
        التخطيط_الرئيسي.setSpacing(20)

        # عنوان الصفحة
        عنوان = QLabel("إدارة البنوك والفروع")
        عنوان.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            padding: 15px;
            background-color: #ecf0f1;
            border-radius: 8px;
            border-left: 5px solid #3498db;
        """)
        التخطيط_الرئيسي.addWidget(عنوان)

        # شريط الأدوات
        self.إنشاء_شريط_الأدوات(التخطيط_الرئيسي)

        # شجرة البنوك
        self.إنشاء_شجرة_البنوك(التخطيط_الرئيسي)

    def إنشاء_شريط_الأدوات(self, التخطيط_الرئيسي):
        """
        إنشاء شريط الأدوات مع الأزرار الرئيسية
        """
        إطار_الأدوات = QFrame()
        إطار_الأدوات.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 1px solid #bdc3c7;
                border-radius: 8px;
                padding: 10px;
            }
        """)

        تخطيط_الأدوات = QHBoxLayout(إطار_الأدوات)
        تخطيط_الأدوات.setSpacing(10)

        # زر إضافة بنك رئيسي
        زر_إضافة_بنك = QPushButton("إضافة بنك رئيسي")
        زر_إضافة_بنك.setIcon(QIcon("icons/add_bank.png"))
        زر_إضافة_بنك.clicked.connect(self.إضافة_بنك_رئيسي)
        زر_إضافة_بنك.setStyleSheet(self.نمط_الزر("#27ae60"))

        # زر إضافة فرع
        زر_إضافة_فرع = QPushButton("إضافة فرع")
        زر_إضافة_فرع.setIcon(QIcon("icons/add_branch.png"))
        زر_إضافة_فرع.clicked.connect(self.إضافة_فرع)
        زر_إضافة_فرع.setStyleSheet(self.نمط_الزر("#3498db"))

        # زر تعديل
        زر_تعديل = QPushButton("تعديل")
        زر_تعديل.setIcon(QIcon("icons/edit.png"))
        زر_تعديل.clicked.connect(self.تعديل_البنك)
        زر_تعديل.setStyleSheet(self.نمط_الزر("#f39c12"))

        # زر حذف
        زر_حذف = QPushButton("حذف")
        زر_حذف.setIcon(QIcon("icons/delete.png"))
        زر_حذف.clicked.connect(self.حذف_البنك)
        زر_حذف.setStyleSheet(self.نمط_الزر("#e74c3c"))

        # زر تحديث
        زر_تحديث = QPushButton("تحديث")
        زر_تحديث.setIcon(QIcon("icons/refresh.png"))
        زر_تحديث.clicked.connect(self.تحديث_البيانات)
        زر_تحديث.setStyleSheet(self.نمط_الزر("#9b59b6"))

        تخطيط_الأدوات.addWidget(زر_إضافة_بنك)
        تخطيط_الأدوات.addWidget(زر_إضافة_فرع)
        تخطيط_الأدوات.addWidget(زر_تعديل)
        تخطيط_الأدوات.addWidget(زر_حذف)
        تخطيط_الأدوات.addWidget(زر_تحديث)
        تخطيط_الأدوات.addStretch()

        التخطيط_الرئيسي.addWidget(إطار_الأدوات)

    def إنشاء_شجرة_البنوك(self, التخطيط_الرئيسي):
        """
        إنشاء شجرة البنوك والفروع
        """
        إطار_الشجرة = QFrame()
        إطار_الشجرة.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 1px solid #bdc3c7;
                border-radius: 8px;
            }
        """)
        إطار_الشجرة.setMinimumWidth(400)
        إطار_الشجرة.setMaximumWidth(500)

        تخطيط_الشجرة = QVBoxLayout(إطار_الشجرة)
        تخطيط_الشجرة.setContentsMargins(15, 15, 15, 15)

        # عنوان الشجرة
        عنوان_الشجرة = QLabel("البنوك والفروع")
        عنوان_الشجرة.setStyleSheet("""
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            padding: 10px;
            background-color: #ecf0f1;
            border-radius: 5px;
        """)
        تخطيط_الشجرة.addWidget(عنوان_الشجرة)

        # شجرة البنوك
        self.شجرة_البنوك = QTreeWidget()
        self.شجرة_البنوك.setHeaderLabels(["اسم البنك", "رقم الحساب", "الحالة"])
        self.شجرة_البنوك.setLayoutDirection(Qt.RightToLeft)
        self.شجرة_البنوك.setAlternatingRowColors(True)
        self.شجرة_البنوك.setStyleSheet("""
            QTreeWidget {
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                background-color: #ffffff;
                font-size: 12px;
                selection-background-color: #3498db;
                selection-color: white;
            }
            QTreeWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTreeWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QTreeWidget::item:hover {
                background-color: #ecf0f1;
            }
        """)

        # ربط إشارة التحديد والدبل كليك
        self.شجرة_البنوك.itemSelectionChanged.connect(self.عند_تحديد_بنك)
        self.شجرة_البنوك.itemDoubleClicked.connect(self.تعديل_البنك)

        تخطيط_الشجرة.addWidget(self.شجرة_البنوك)
        التخطيط_الرئيسي.addWidget(إطار_الشجرة)

    def إنشاء_منطقة_التفاصيل(self, تخطيط_المحتوى):
        """
        إنشاء منطقة تفاصيل البنك المحدد
        """
        إطار_التفاصيل = QFrame()
        إطار_التفاصيل.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 1px solid #bdc3c7;
                border-radius: 8px;
            }
        """)

        تخطيط_التفاصيل = QVBoxLayout(إطار_التفاصيل)
        تخطيط_التفاصيل.setContentsMargins(20, 20, 20, 20)
        تخطيط_التفاصيل.setSpacing(15)

        # عنوان التفاصيل
        عنوان_التفاصيل = QLabel("تفاصيل البنك")
        عنوان_التفاصيل.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            padding: 10px;
            background-color: #ecf0f1;
            border-radius: 5px;
        """)
        تخطيط_التفاصيل.addWidget(عنوان_التفاصيل)

        # نموذج التفاصيل
        self.إنشاء_نموذج_التفاصيل(تخطيط_التفاصيل)

        تخطيط_المحتوى.addWidget(إطار_التفاصيل)

    def إنشاء_نموذج_التفاصيل(self, تخطيط_التفاصيل):
        """
        إنشاء نموذج تفاصيل البنك
        """
        # مجموعة البيانات الأساسية
        مجموعة_أساسية = QGroupBox("البيانات الأساسية")
        مجموعة_أساسية.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2c3e50;
            }
        """)

        تخطيط_أساسي = QFormLayout(مجموعة_أساسية)
        تخطيط_أساسي.setSpacing(10)

        # حقول البيانات
        self.حقل_اسم_البنك = QLineEdit()
        self.حقل_اسم_البنك.setPlaceholderText("أدخل اسم البنك")
        self.حقل_اسم_البنك.setStyleSheet(self.نمط_الحقل())

        self.حقل_رقم_الحساب = QLineEdit()
        self.حقل_رقم_الحساب.setPlaceholderText("أدخل رقم حساب الشركة")
        self.حقل_رقم_الحساب.setStyleSheet(self.نمط_الحقل())

        self.حقل_رقم_الهاتف = QLineEdit()
        self.حقل_رقم_الهاتف.setPlaceholderText("أدخل رقم الهاتف (اختياري)")
        self.حقل_رقم_الهاتف.setStyleSheet(self.نمط_الحقل())

        self.حقل_العنوان = QTextEdit()
        self.حقل_العنوان.setPlaceholderText("أدخل عنوان البنك (اختياري)")
        self.حقل_العنوان.setMaximumHeight(80)
        self.حقل_العنوان.setStyleSheet("""
            QTextEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 12px;
                background-color: #ffffff;
                font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
            }
            QTextEdit:focus {
                border-color: #3498db;
                background-color: #f8f9fa;
            }
        """)

        self.قائمة_البنك_الرئيسي = QComboBox()
        self.قائمة_البنك_الرئيسي.addItem("-- بنك رئيسي --", None)
        self.قائمة_البنك_الرئيسي.setStyleSheet(self.نمط_القائمة())

        self.مربع_الحالة = QCheckBox("البنك نشط")
        self.مربع_الحالة.setChecked(True)
        self.مربع_الحالة.setStyleSheet("""
            QCheckBox {
                font-size: 12px;
                color: #2c3e50;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QCheckBox::indicator:checked {
                background-color: #27ae60;
                border: 2px solid #27ae60;
            }
        """)

        تخطيط_أساسي.addRow("اسم البنك:", self.حقل_اسم_البنك)
        تخطيط_أساسي.addRow("رقم الحساب:", self.حقل_رقم_الحساب)
        تخطيط_أساسي.addRow("رقم الهاتف:", self.حقل_رقم_الهاتف)
        تخطيط_أساسي.addRow("العنوان:", self.حقل_العنوان)
        تخطيط_أساسي.addRow("البنك الرئيسي:", self.قائمة_البنك_الرئيسي)
        تخطيط_أساسي.addRow("", self.مربع_الحالة)

        تخطيط_التفاصيل.addWidget(مجموعة_أساسية)

        # أزرار الحفظ والإلغاء
        تخطيط_الأزرار = QHBoxLayout()

        زر_حفظ = QPushButton("حفظ")
        زر_حفظ.setIcon(QIcon("icons/save.png"))
        زر_حفظ.clicked.connect(self.حفظ_البنك)
        زر_حفظ.setStyleSheet(self.نمط_الزر("#27ae60"))

        زر_إلغاء = QPushButton("إلغاء")
        زر_إلغاء.setIcon(QIcon("icons/cancel.png"))
        زر_إلغاء.clicked.connect(self.إلغاء_التعديل)
        زر_إلغاء.setStyleSheet(self.نمط_الزر("#95a5a6"))

        تخطيط_الأزرار.addStretch()
        تخطيط_الأزرار.addWidget(زر_حفظ)
        تخطيط_الأزرار.addWidget(زر_إلغاء)

        تخطيط_التفاصيل.addLayout(تخطيط_الأزرار)
        تخطيط_التفاصيل.addStretch()

    def نمط_الزر(self, لون_أساسي):
        """
        نمط موحد للأزرار
        """
        return f"""
            QPushButton {{
                background-color: {لون_أساسي};
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background-color: {لون_أساسي}dd;
                
            }}
            QPushButton:pressed {{
                background-color: {لون_أساسي}aa;
            }}
        """

    def نمط_الحقل(self):
        """
        نمط موحد للحقول النصية
        """
        return """
            QLineEdit {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 12px;
                background-color: #ffffff;
            }
            QLineEdit:focus {
                border-color: #3498db;
                background-color: #f8f9fa;
            }
        """

    def نمط_القائمة(self):
        """
        نمط موحد للقوائم المنسدلة
        """
        return """
            QComboBox {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 12px;
                background-color: #ffffff;
                min-width: 150px;
            }
            QComboBox:focus {
                border-color: #3498db;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: url(icons/arrow_down.png);
                width: 12px;
                height: 12px;
            }
        """

    def تحميل_البيانات(self):
        """
        تحميل بيانات البنوك من قاعدة البيانات
        """
        try:
            # مسح الشجرة
            self.شجرة_البنوك.clear()

            # تحميل البنوك الرئيسية
            استعلام = """
                SELECT رقم_البنك, اسم_البنك, رقم_حساب_الشركة, رقم_الهاتف, العنوان, حالة_النشاط
                FROM البنوك
                WHERE بنك_رئيسي IS NULL
                ORDER BY اسم_البنك
            """
            self.قاعدة_البيانات.cursor.execute(استعلام)
            البنوك_الرئيسية = self.قاعدة_البيانات.cursor.fetchall()

            # إضافة البنوك الرئيسية للشجرة
            for بنك in البنوك_الرئيسية:
                عنصر_بنك = QTreeWidgetItem(self.شجرة_البنوك)
                عنصر_بنك.setText(0, بنك[1])  # اسم البنك
                عنصر_بنك.setText(1, بنك[2] or "غير محدد")  # رقم الحساب
                عنصر_بنك.setText(2, "نشط" if بنك[5] else "غير نشط")  # الحالة (تحديث الفهرس)
                عنصر_بنك.setData(0, Qt.UserRole, بنك[0])  # رقم البنك
                عنصر_بنك.setData(1, Qt.UserRole, "بنك_رئيسي")  # نوع العنصر

                # إضافة tooltip مع معلومات إضافية
                tooltip_text = f"البنك: {بنك[1]}\n"
                if بنك[2]:
                    tooltip_text += f"رقم الحساب: {بنك[2]}\n"
                if بنك[3]:
                    tooltip_text += f"الهاتف: {بنك[3]}\n"
                if بنك[4]:
                    tooltip_text += f"العنوان: {بنك[4][:50]}{'...' if len(بنك[4]) > 50 else ''}\n"
                tooltip_text += f"الحالة: {'نشط' if بنك[5] else 'غير نشط'}"
                عنصر_بنك.setToolTip(0, tooltip_text)

                # تلوين حسب الحالة
                if بنك[5]:
                    عنصر_بنك.setForeground(0, QBrush(QColor("#27ae60")))
                else:
                    عنصر_بنك.setForeground(0, QBrush(QColor("#e74c3c")))

                # تحميل الفروع
                self.تحميل_فروع_البنك(بنك[0], عنصر_بنك)

            # توسيع جميع العناصر
            self.شجرة_البنوك.expandAll()

            # تحديث قائمة البنوك الرئيسية
            self.تحديث_قائمة_البنوك_الرئيسية()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل البيانات: {str(e)}")

    def تحميل_فروع_البنك(self, رقم_البنك_الرئيسي, عنصر_البنك_الرئيسي):
        """
        تحميل فروع بنك معين
        """
        try:
            استعلام = """
                SELECT رقم_البنك, اسم_البنك, رقم_حساب_الشركة, رقم_الهاتف, العنوان, حالة_النشاط
                FROM البنوك
                WHERE بنك_رئيسي = %s
                ORDER BY اسم_البنك
            """
            self.قاعدة_البيانات.cursor.execute(استعلام, (رقم_البنك_الرئيسي,))
            الفروع = self.قاعدة_البيانات.cursor.fetchall()

            for فرع in الفروع:
                عنصر_فرع = QTreeWidgetItem(عنصر_البنك_الرئيسي)
                عنصر_فرع.setText(0, f"🏢 {فرع[1]}")  # اسم الفرع مع أيقونة
                عنصر_فرع.setText(1, فرع[2] or "غير محدد")  # رقم الحساب
                عنصر_فرع.setText(2, "نشط" if فرع[5] else "غير نشط")  # الحالة (تحديث الفهرس)
                عنصر_فرع.setData(0, Qt.UserRole, فرع[0])  # رقم البنك
                عنصر_فرع.setData(1, Qt.UserRole, "فرع")  # نوع العنصر

                # إضافة tooltip مع معلومات إضافية للفرع
                tooltip_text = f"الفرع: {فرع[1]}\n"
                if فرع[2]:
                    tooltip_text += f"رقم الحساب: {فرع[2]}\n"
                if فرع[3]:
                    tooltip_text += f"الهاتف: {فرع[3]}\n"
                if فرع[4]:
                    tooltip_text += f"العنوان: {فرع[4][:50]}{'...' if len(فرع[4]) > 50 else ''}\n"
                tooltip_text += f"الحالة: {'نشط' if فرع[5] else 'غير نشط'}"
                عنصر_فرع.setToolTip(0, tooltip_text)

                # تلوين حسب الحالة
                if فرع[5]:
                    عنصر_فرع.setForeground(0, QBrush(QColor("#3498db")))
                else:
                    عنصر_فرع.setForeground(0, QBrush(QColor("#e74c3c")))

        except Exception as e:
            print(f"خطأ في تحميل الفروع: {str(e)}")

    def تحديث_قائمة_البنوك_الرئيسية(self):
        """
        تحديث قائمة البنوك الرئيسية في ComboBox
        """
        try:
            # مسح القائمة
            self.قائمة_البنك_الرئيسي.clear()
            self.قائمة_البنك_الرئيسي.addItem("-- بنك رئيسي --", None)

            # تحميل البنوك الرئيسية
            استعلام = """
                SELECT رقم_البنك, اسم_البنك
                FROM البنوك
                WHERE بنك_رئيسي IS NULL AND حالة_النشاط = TRUE
                ORDER BY اسم_البنك
            """
            self.قاعدة_البيانات.cursor.execute(استعلام)
            البنوك = self.قاعدة_البيانات.cursor.fetchall()

            for بنك in البنوك:
                self.قائمة_البنك_الرئيسي.addItem(بنك[1], بنك[0])

        except Exception as e:
            print(f"خطأ في تحديث قائمة البنوك: {str(e)}")

    def عند_تحديد_بنك(self):
        """
        عند تحديد بنك من الشجرة
        """
        العناصر_المحددة = self.شجرة_البنوك.selectedItems()
        if العناصر_المحددة:
            العنصر = العناصر_المحددة[0]
            رقم_البنك = العنصر.data(0, Qt.UserRole)
            نوع_العنصر = العنصر.data(1, Qt.UserRole)

            if رقم_البنك:
                self.البنك_المحدد = رقم_البنك
                self.تحميل_تفاصيل_البنك(رقم_البنك)

                # تحديث عنوان التفاصيل حسب نوع العنصر
                if نوع_العنصر == "فرع":
                    # يمكن إضافة منطق خاص بالفروع هنا
                    pass
            else:
                self.مسح_النموذج()

    def تحميل_تفاصيل_البنك(self, رقم_البنك):
        """
        تحميل تفاصيل البنك المحدد
        """
        try:
            استعلام = """
                SELECT اسم_البنك, بنك_رئيسي, رقم_حساب_الشركة, رقم_الهاتف, العنوان, حالة_النشاط
                FROM البنوك
                WHERE رقم_البنك = %s
            """
            self.قاعدة_البيانات.cursor.execute(استعلام, (رقم_البنك,))
            البيانات = self.قاعدة_البيانات.cursor.fetchone()

            if البيانات:
                self.حقل_اسم_البنك.setText(البيانات[0])
                self.حقل_رقم_الحساب.setText(البيانات[2] or "")
                self.حقل_رقم_الهاتف.setText(البيانات[3] or "")
                self.حقل_العنوان.setPlainText(البيانات[4] or "")
                self.مربع_الحالة.setChecked(البيانات[5])

                # تحديد البنك الرئيسي
                if البيانات[1]:
                    for i in range(self.قائمة_البنك_الرئيسي.count()):
                        if self.قائمة_البنك_الرئيسي.itemData(i) == البيانات[1]:
                            self.قائمة_البنك_الرئيسي.setCurrentIndex(i)
                            break
                else:
                    self.قائمة_البنك_الرئيسي.setCurrentIndex(0)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل تفاصيل البنك: {str(e)}")

    def مسح_النموذج(self):
        """
        مسح جميع حقول النموذج
        """
        self.حقل_اسم_البنك.clear()
        self.حقل_رقم_الحساب.clear()
        self.حقل_رقم_الهاتف.clear()
        self.حقل_العنوان.clear()
        self.قائمة_البنك_الرئيسي.setCurrentIndex(0)
        self.مربع_الحالة.setChecked(True)
        self.البنك_المحدد = None

    def إضافة_بنك_رئيسي(self):
        """
        إضافة بنك رئيسي جديد
        """
        نافذة_إضافة = نافذة_بنك(self, "إضافة بنك رئيسي")
        if نافذة_إضافة.exec_() == QDialog.Accepted:
            self.تحميل_البيانات()

    def إضافة_فرع(self):
        """
        إضافة فرع لبنك موجود
        """
        نافذة_إضافة = نافذة_بنك(self, "إضافة فرع")
        if نافذة_إضافة.exec_() == QDialog.Accepted:
            self.تحميل_البيانات()

    def تعديل_البنك(self):
        """
        تعديل البنك المحدد
        """
        if not self.البنك_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد بنك للتعديل")
            return

        self.حقل_اسم_البنك.setFocus()
        QMessageBox.information(self, "تعديل بنك", "عدل البيانات المطلوبة ثم اضغط حفظ")

    def حذف_البنك(self):
        """
        حذف البنك المحدد
        """
        if not self.البنك_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد بنك للحذف")
            return

        # التحقق من وجود فروع أو عملاء مرتبطين
        if self.التحقق_من_الارتباطات(self.البنك_المحدد):
            QMessageBox.warning(self, "تحذير",
                              "لا يمكن حذف هذا البنك لوجود فروع أو عملاء مرتبطين به.\n"
                              "يرجى حذف الارتباطات أولاً أو إلغاء تفعيل البنك بدلاً من حذفه.")
            return

        # تأكيد الحذف
        رد = QMessageBox.question(self, "تأكيد الحذف",
                                 "هل أنت متأكد من حذف هذا البنك؟\n"
                                 "هذا الإجراء لا يمكن التراجع عنه.",
                                 QMessageBox.Yes | QMessageBox.No)

        if رد == QMessageBox.Yes:
            try:
                استعلام = "DELETE FROM البنوك WHERE رقم_البنك = %s"
                self.قاعدة_البيانات.cursor.execute(استعلام, (self.البنك_المحدد,))
                self.قاعدة_البيانات.connection.commit()

                QMessageBox.information(self, "نجح", "تم حذف البنك بنجاح")
                self.تحديث_البيانات()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف البنك: {str(e)}")

    def التحقق_من_الارتباطات(self, رقم_البنك):
        """
        التحقق من وجود ارتباطات للبنك
        """
        try:
            # التحقق من الفروع
            self.قاعدة_البيانات.cursor.execute(
                "SELECT COUNT(*) FROM البنوك WHERE بنك_رئيسي = %s",
                (رقم_البنك,)
            )
            عدد_الفروع = self.قاعدة_البيانات.cursor.fetchone()[0]

            # التحقق من العملاء
            self.قاعدة_البيانات.cursor.execute(
                "SELECT COUNT(*) FROM العملاء WHERE رقم_البنك = %s",
                (رقم_البنك,)
            )
            عدد_العملاء = self.قاعدة_البيانات.cursor.fetchone()[0]

            # التحقق من العقود
            self.قاعدة_البيانات.cursor.execute(
                "SELECT COUNT(*) FROM العقود WHERE رقم_البنك = %s",
                (رقم_البنك,)
            )
            عدد_العقود = self.قاعدة_البيانات.cursor.fetchone()[0]

            return عدد_الفروع > 0 or عدد_العملاء > 0 or عدد_العقود > 0

        except Exception as e:
            print(f"خطأ في التحقق من الارتباطات: {str(e)}")
            return True  # في حالة الخطأ، نمنع الحذف للأمان

    def حفظ_البنك(self):
        """
        حفظ بيانات البنك (إضافة أو تعديل)
        """
        # التحقق من صحة البيانات
        if not self.التحقق_من_البيانات():
            return

        اسم_البنك = self.حقل_اسم_البنك.text().strip()
        رقم_الحساب = self.حقل_رقم_الحساب.text().strip()
        رقم_الهاتف = self.حقل_رقم_الهاتف.text().strip()
        العنوان = self.حقل_العنوان.toPlainText().strip()
        بنك_رئيسي = self.قائمة_البنك_الرئيسي.currentData()
        حالة_النشاط = self.مربع_الحالة.isChecked()

        # تحويل القيم الفارغة إلى None
        رقم_الحساب = رقم_الحساب if رقم_الحساب else None
        رقم_الهاتف = رقم_الهاتف if رقم_الهاتف else None
        العنوان = العنوان if العنوان else None

        try:
            if self.البنك_المحدد:
                # تعديل بنك موجود
                استعلام = """
                    UPDATE البنوك
                    SET اسم_البنك = %s, بنك_رئيسي = %s, رقم_حساب_الشركة = %s,
                        رقم_الهاتف = %s, العنوان = %s, حالة_النشاط = %s
                    WHERE رقم_البنك = %s
                """
                self.قاعدة_البيانات.cursor.execute(استعلام,
                    (اسم_البنك, بنك_رئيسي, رقم_الحساب, رقم_الهاتف, العنوان, حالة_النشاط, self.البنك_المحدد))
                رسالة = "تم تعديل البنك بنجاح"
            else:
                # إضافة بنك جديد
                استعلام = """
                    INSERT INTO البنوك (اسم_البنك, بنك_رئيسي, رقم_حساب_الشركة, رقم_الهاتف, العنوان, حالة_النشاط)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """
                self.قاعدة_البيانات.cursor.execute(استعلام,
                    (اسم_البنك, بنك_رئيسي, رقم_الحساب, رقم_الهاتف, العنوان, حالة_النشاط))
                رسالة = "تم إضافة البنك بنجاح"

            self.قاعدة_البيانات.connection.commit()
            QMessageBox.information(self, "نجح", رسالة)
            self.تحديث_البيانات()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ البنك: {str(e)}")

    def التحقق_من_البيانات(self):
        """
        التحقق من صحة البيانات المدخلة
        """
        if not self.حقل_اسم_البنك.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم البنك")
            self.حقل_اسم_البنك.setFocus()
            return False

        # التحقق من عدم تكرار اسم البنك
        اسم_البنك = self.حقل_اسم_البنك.text().strip()
        try:
            if self.البنك_المحدد:
                # في حالة التعديل، تجاهل البنك الحالي
                استعلام = "SELECT COUNT(*) FROM البنوك WHERE اسم_البنك = %s AND رقم_البنك != %s"
                self.قاعدة_البيانات.cursor.execute(استعلام, (اسم_البنك, self.البنك_المحدد))
            else:
                # في حالة الإضافة
                استعلام = "SELECT COUNT(*) FROM البنوك WHERE اسم_البنك = %s"
                self.قاعدة_البيانات.cursor.execute(استعلام, (اسم_البنك,))

            if self.قاعدة_البيانات.cursor.fetchone()[0] > 0:
                QMessageBox.warning(self, "تحذير", "اسم البنك موجود مسبقاً")
                self.حقل_اسم_البنك.setFocus()
                return False

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في التحقق من البيانات: {str(e)}")
            return False

        return True

    def إلغاء_التعديل(self):
        """
        إلغاء التعديل والعودة للحالة السابقة
        """
        if self.البنك_المحدد:
            self.تحميل_تفاصيل_البنك(self.البنك_المحدد)
        else:
            self.مسح_النموذج()

    def تحديث_البيانات(self):
        """
        تحديث جميع البيانات
        """
        self.تحميل_البيانات()
        self.مسح_النموذج()

class واجهة_العقود(QWidget):
    """
    واجهة إدارة العقود
    """
    
    def __init__(self):
        """
        دالة التهيئة لواجهة العقود
        """
        super().__init__()
        self.قاعدة_البيانات = قاعدة_البيانات()
        self.قاعدة_البيانات.اتصال_قاعدة_البيانات()
        self.قاعدة_البيانات.cursor.execute(f"USE {self.قاعدة_البيانات.database}")
        
        self.إعداد_الواجهة()
    
    def إعداد_الواجهة(self):
        """
        دالة إعداد واجهة العقود
        """
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(20, 20, 20, 20)
        التخطيط_الرئيسي.setSpacing(20)
        
        # عنوان الصفحة
        عنوان = QLabel("إدارة العقود")
        عنوان.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            padding: 15px;
            background-color: #ecf0f1;
            border-radius: 8px;
            border-left: 5px solid #e67e22;
        """)
        التخطيط_الرئيسي.addWidget(عنوان)
        
        # محتوى مؤقت
        محتوى = QLabel("واجهة إدارة العقود - قيد التطوير")
        محتوى.setAlignment(Qt.AlignCenter)
        محتوى.setStyleSheet("font-size: 18px; color: #7f8c8d; margin: 50px;")
        التخطيط_الرئيسي.addWidget(محتوى)
        
        # معلومات إضافية
        معلومات = QLabel("""
        ستتضمن هذه الواجهة:
        • إنشاء عقود البيع بالأقساط
        • ربط العقود بالعملاء والبنوك
        • تحديد شروط السداد
        • متابعة حالة العقود
        • تجديد وإلغاء العقود
        """)
        معلومات.setStyleSheet("font-size: 14px; color: #34495e; padding: 20px;")
        التخطيط_الرئيسي.addWidget(معلومات)

class واجهة_الأقساط(QWidget):
    """
    واجهة إدارة الأقساط
    """
    
    def __init__(self):
        """
        دالة التهيئة لواجهة الأقساط
        """
        super().__init__()
        self.قاعدة_البيانات = قاعدة_البيانات()
        self.قاعدة_البيانات.اتصال_قاعدة_البيانات()
        self.قاعدة_البيانات.cursor.execute(f"USE {self.قاعدة_البيانات.database}")
        
        self.إعداد_الواجهة()
    
    def إعداد_الواجهة(self):
        """
        دالة إعداد واجهة الأقساط
        """
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(20, 20, 20, 20)
        التخطيط_الرئيسي.setSpacing(20)
        
        # عنوان الصفحة
        عنوان = QLabel("إدارة الديون والأقساط")
        عنوان.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            padding: 15px;
            background-color: #ecf0f1;
            border-radius: 8px;
            border-left: 5px solid #e74c3c;
        """)
        التخطيط_الرئيسي.addWidget(عنوان)
        
        # محتوى مؤقت
        محتوى = QLabel("واجهة إدارة الديون والأقساط - قيد التطوير")
        محتوى.setAlignment(Qt.AlignCenter)
        محتوى.setStyleSheet("font-size: 18px; color: #7f8c8d; margin: 50px;")
        التخطيط_الرئيسي.addWidget(محتوى)
        
        # معلومات إضافية
        معلومات = QLabel("""
        ستتضمن هذه الواجهة:
        • متابعة أقساط العملاء
        • تسجيل المدفوعات
        • تنبيهات الأقساط المتأخرة
        • كشوف حسابات العملاء
        • تقارير الديون والمتحصلات
        """)
        معلومات.setStyleSheet("font-size: 14px; color: #34495e; padding: 20px;")
        التخطيط_الرئيسي.addWidget(معلومات)

class واجهة_التقارير_المالية(QWidget):
    """
    واجهة التقارير المالية
    """
    
    def __init__(self):
        """
        دالة التهيئة لواجهة التقارير المالية
        """
        super().__init__()
        self.قاعدة_البيانات = قاعدة_البيانات()
        self.قاعدة_البيانات.اتصال_قاعدة_البيانات()
        self.قاعدة_البيانات.cursor.execute(f"USE {self.قاعدة_البيانات.database}")
        
        self.إعداد_الواجهة()
    
    def إعداد_الواجهة(self):
        """
        دالة إعداد واجهة التقارير المالية
        """
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(20, 20, 20, 20)
        التخطيط_الرئيسي.setSpacing(20)
        
        # عنوان الصفحة
        عنوان = QLabel("التقارير المالية")
        عنوان.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            padding: 15px;
            background-color: #ecf0f1;
            border-radius: 8px;
            border-left: 5px solid #27ae60;
        """)
        التخطيط_الرئيسي.addWidget(عنوان)
        
        # محتوى مؤقت
        محتوى = QLabel("واجهة التقارير المالية - قيد التطوير")
        محتوى.setAlignment(Qt.AlignCenter)
        محتوى.setStyleSheet("font-size: 18px; color: #7f8c8d; margin: 50px;")
        التخطيط_الرئيسي.addWidget(محتوى)
        
        # معلومات إضافية
        معلومات = QLabel("""
        ستتضمن هذه الواجهة:
        • تقارير المبيعات اليومية والشهرية
        • تقارير الأرباح والخسائر
        • تقارير المخزون والحركة
        • تقارير العملاء والموردين
        • التحليلات المالية المتقدمة
        """)
        معلومات.setStyleSheet("font-size: 14px; color: #34495e; padding: 20px;")
        التخطيط_الرئيسي.addWidget(معلومات)

class واجهة_الإعدادات(QWidget):
    """
    واجهة الإعدادات العامة
    """
    
    def __init__(self):
        """
        دالة التهيئة لواجهة الإعدادات
        """
        super().__init__()
        self.إعداد_الواجهة()
    
    def إعداد_الواجهة(self):
        """
        دالة إعداد واجهة الإعدادات
        """
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(20, 20, 20, 20)
        التخطيط_الرئيسي.setSpacing(20)
        
        # عنوان الصفحة
        عنوان = QLabel("الإعدادات العامة")
        عنوان.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            padding: 15px;
            background-color: #ecf0f1;
            border-radius: 8px;
            border-left: 5px solid #95a5a6;
        """)
        التخطيط_الرئيسي.addWidget(عنوان)
        
        # محتوى مؤقت
        محتوى = QLabel("واجهة الإعدادات العامة - قيد التطوير")
        محتوى.setAlignment(Qt.AlignCenter)
        محتوى.setStyleSheet("font-size: 18px; color: #7f8c8d; margin: 50px;")
        التخطيط_الرئيسي.addWidget(محتوى)
        
        # معلومات إضافية
        معلومات = QLabel("""
        ستتضمن هذه الواجهة:
        • إعدادات قاعدة البيانات
        • إعدادات الطباعة والتقارير
        • إدارة المستخدمين والصلاحيات
        • إعدادات النسخ الاحتياطي
        • تخصيص واجهة المستخدم
        """)
        معلومات.setStyleSheet("font-size: 14px; color: #34495e; padding: 20px;")
        التخطيط_الرئيسي.addWidget(معلومات)

class واجهة_الموردين(QWidget):
    """واجهة إدارة الموردين الرئيسية"""

    def __init__(self):
        super().__init__()
        self.قاعدة_البيانات = قاعدة_البيانات()
        self.قاعدة_البيانات.اتصال_قاعدة_البيانات()
        self.قاعدة_البيانات.cursor.execute(f"USE {self.قاعدة_البيانات.database}")
        self.المورد_المحدد = None

        self.إعداد_الواجهة()
        self.تطبيق_الأنماط()
        self.تحميل_الموردين()

        # تعطيل أزرار التعديل والحذف في البداية
        self.أزرار_الإجراءات["edit_supplier"].setEnabled(False)
        self.أزرار_الإجراءات["delete_supplier"].setEnabled(False)
        self.أزرار_الإجراءات["view_details"].setEnabled(False)
        self.أزرار_الإجراءات["supplier_report"].setEnabled(False)

    def إعداد_الواجهة(self):
        """
        دالة إعداد واجهة إدارة الموردين
        """
        التخطيط_الرئيسي = QVBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(20, 20, 20, 20)
        التخطيط_الرئيسي.setSpacing(20)

        # عنوان الصفحة
        عنوان = QLabel("إدارة الموردين")
        عنوان.setObjectName("page_title")
        التخطيط_الرئيسي.addWidget(عنوان)

        # شريط الفلاتر
        self.إنشاء_شريط_الفلاتر(التخطيط_الرئيسي)

        # شريط الأزرار
        self.إنشاء_شريط_الأزرار(التخطيط_الرئيسي)

        # منطقة البيانات
        self.إنشاء_منطقة_البيانات(التخطيط_الرئيسي)

    def إنشاء_شريط_الفلاتر(self, التخطيط_الرئيسي):
        """
        دالة إنشاء شريط الفلاتر والبحث
        """
        إطار_الفلاتر = QFrame()
        إطار_الفلاتر.setObjectName("filters_bar")
        إطار_الفلاتر.setFixedHeight(80)

        تخطيط_الفلاتر = QHBoxLayout(إطار_الفلاتر)
        تخطيط_الفلاتر.setContentsMargins(20, 15, 20, 15)
        تخطيط_الفلاتر.setSpacing(20)

        # حقل البحث
        تسمية_البحث = QLabel("البحث:")
        تسمية_البحث.setStyleSheet("font-weight: bold; color: #2c3e50;")

        self.حقل_البحث = QLineEdit()
        self.حقل_البحث.setObjectName("search_field")
        self.حقل_البحث.setPlaceholderText("اسم المورد، رقم الهاتف، أو العنوان...")
        self.حقل_البحث.setFixedHeight(40)
        self.حقل_البحث.textChanged.connect(self.البحث_عن_الموردين)

        # زر البحث
        زر_البحث = QPushButton("🔍")
        زر_البحث.setObjectName("search_button")
        زر_البحث.setFixedSize(40, 40)
        زر_البحث.clicked.connect(self.البحث_عن_الموردين)

        # فلتر الحالة
        تسمية_حالة = QLabel("الحالة:")
        تسمية_حالة.setStyleSheet("font-weight: bold; color: #2c3e50;")

        self.قائمة_حالة = QComboBox()
        self.قائمة_حالة.addItems(["الكل", "نشط", "غير نشط"])
        self.قائمة_حالة.setFixedHeight(40)
        self.قائمة_حالة.currentTextChanged.connect(self.البحث_عن_الموردين)

        # زر الفلتر المتقدم
        زر_فلتر = QPushButton("فلتر متقدم")
        زر_فلتر.setObjectName("filter_button")
        زر_فلتر.setFixedHeight(40)
        زر_فلتر.clicked.connect(self.فلتر_متقدم)

        تخطيط_الفلاتر.addWidget(تسمية_البحث)
        تخطيط_الفلاتر.addWidget(self.حقل_البحث, 3)
        تخطيط_الفلاتر.addWidget(زر_البحث)
        تخطيط_الفلاتر.addWidget(تسمية_حالة)
        تخطيط_الفلاتر.addWidget(self.قائمة_حالة)
        تخطيط_الفلاتر.addWidget(زر_فلتر)
        تخطيط_الفلاتر.addStretch()

        التخطيط_الرئيسي.addWidget(إطار_الفلاتر)

    def إنشاء_شريط_الأزرار(self, التخطيط_الرئيسي):
        """
        دالة إنشاء شريط أزرار الإجراءات
        """
        إطار_الأزرار = QFrame()
        إطار_الأزرار.setObjectName("actions_bar")
        إطار_الأزرار.setFixedHeight(100)

        تخطيط_الأزرار = QHBoxLayout(إطار_الأزرار)
        تخطيط_الأزرار.setContentsMargins(20, 15, 20, 15)
        تخطيط_الأزرار.setSpacing(15)

        # أزرار الإجراءات
        أزرار = [
            ("➕", "إضافة مورد", "add_supplier", "#27ae60", self.إضافة_مورد),
            ("✏️", "تعديل", "edit_supplier", "#3498db", self.تعديل_مورد),
            ("🗑️", "حذف", "delete_supplier", "#e74c3c", self.حذف_مورد),
            ("👁️", "عرض التفاصيل", "view_details", "#9b59b6", self.عرض_تفاصيل_مورد),
            ("📊", "تقرير المورد", "supplier_report", "#f39c12", self.تقرير_مورد),
            ("🖨️", "طباعة", "print_suppliers", "#7f8c8d", self.طباعة_الموردين),
            ("📤", "تصدير", "export_suppliers", "#34495e", self.تصدير_الموردين)
        ]

        self.أزرار_الإجراءات = {}

        for أيقونة, نص, مفتاح, لون, وظيفة in أزرار:
            زر = self.إنشاء_زر_إجراء(أيقونة, نص, لون)
            زر.clicked.connect(وظيفة)
            self.أزرار_الإجراءات[مفتاح] = زر
            تخطيط_الأزرار.addWidget(زر)

        تخطيط_الأزرار.addStretch()
        التخطيط_الرئيسي.addWidget(إطار_الأزرار)

    def إنشاء_زر_إجراء(self, أيقونة, نص, لون):
        """
        دالة إنشاء زر إجراء مخصص
        """
        زر = QPushButton()
        زر.setFixedSize(100, 70)
        زر.setObjectName("action_button")

        تخطيط_زر = QVBoxLayout(زر)
        تخطيط_زر.setContentsMargins(5, 5, 5, 5)

        # الأيقونة
        تسمية_أيقونة = QLabel(أيقونة)
        تسمية_أيقونة.setAlignment(Qt.AlignCenter)
        تسمية_أيقونة.setStyleSheet("font-size: 24px;")

        # النص
        تسمية_نص = QLabel(نص)
        تسمية_نص.setAlignment(Qt.AlignCenter)
        تسمية_نص.setStyleSheet("font-size: 10px; font-weight: bold;")
        تسمية_نص.setWordWrap(True)

        تخطيط_زر.addWidget(تسمية_أيقونة)
        تخطيط_زر.addWidget(تسمية_نص)

        return زر

    def إنشاء_منطقة_البيانات(self, التخطيط_الرئيسي):
        """
        دالة إنشاء منطقة عرض البيانات
        """
        منطقة_البيانات = QStackedWidget()
        منطقة_البيانات.setObjectName("data_area")

        # جدول الموردين
        self.جدول_الموردين = QTableWidget()
        self.جدول_الموردين.setObjectName("suppliers_table")

        # إعداد الأعمدة
        الأعمدة = [
            "رقم المورد", "اسم المورد", "العنوان", "رقم الهاتف",
            "البريد الإلكتروني", "الرصيد الحالي", "الحالة", "تاريخ الإضافة"
        ]

        self.جدول_الموردين.setColumnCount(len(الأعمدة))
        self.جدول_الموردين.setHorizontalHeaderLabels(الأعمدة)

        # إعداد خصائص الجدول
        self.جدول_الموردين.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.جدول_الموردين.setAlternatingRowColors(True)
        self.جدول_الموردين.setSortingEnabled(True)

        # تعديل عرض الأعمدة
        header = self.جدول_الموردين.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # اسم المورد
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # العنوان

        # ربط النقر بتحديد المورد
        self.جدول_الموردين.itemSelectionChanged.connect(self.تحديد_مورد)

        # قائمة سياق
        self.جدول_الموردين.setContextMenuPolicy(Qt.CustomContextMenu)
        self.جدول_الموردين.customContextMenuRequested.connect(self.عرض_قائمة_سياق)

        منطقة_البيانات.addWidget(self.جدول_الموردين)
        التخطيط_الرئيسي.addWidget(منطقة_البيانات)

    def تطبيق_الأنماط(self):
        """
        دالة تطبيق الأنماط على الواجهة
        """
        self.setStyleSheet("""
            QLabel#page_title {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                                           stop:0 #ecf0f1,
                                           stop:1 #ffffff);
                border-radius: 10px;
                border-left: 5px solid #e67e22;
            }

            QFrame#filters_bar, QFrame#actions_bar {
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
            }

            QStackedWidget#data_area {
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
            }

            QTableWidget#suppliers_table {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #3498db;
                selection-color: white;
                border: none;
                border-radius: 8px;
                font-size: 12px;
            }

            QTableWidget#suppliers_table::item {
                padding: 10px;
                border-bottom: 1px solid #dee2e6;
            }

            QTableWidget#suppliers_table::item:selected {
                background-color: #3498db;
                color: white;
            }

            QTableWidget#suppliers_table::item:hover {
                background-color: #e3f2fd;
            }

            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                           stop:0 #e67e22,
                                           stop:1 #d35400);
                color: white;
                padding: 12px;
                border: none;
                font-weight: bold;
                font-size: 13px;
            }

            QHeaderView::section:hover {
                background-color: #f39c12;
            }
        """)

    def تحميل_الموردين(self):
        """
        دالة تحميل جميع الموردين
        """
        try:
            استعلام = """
            SELECT رقم_المورد, اسم_المورد, العنوان, رقم_الهاتف,
                   البريد_الإلكتروني, الرصيد_الحالي, حالة_النشاط,
                   DATE_FORMAT(تاريخ_الإضافة, '%Y-%m-%d') as تاريخ_الإضافة
            FROM الموردين
            ORDER BY تاريخ_الإضافة DESC
            """

            الموردين = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام)

            if الموردين:
                self.جدول_الموردين.setRowCount(len(الموردين))

                for صف, مورد in enumerate(الموردين):
                    for عمود, قيمة in enumerate(مورد):
                        if عمود == 6:  # عمود الحالة
                            حالة = "نشط" if قيمة == "نشط" else "غير نشط"
                            عنصر = QTableWidgetItem(حالة)
                            if حالة == "نشط":
                                عنصر.setForeground(QColor("#27ae60"))
                            else:
                                عنصر.setForeground(QColor("#e74c3c"))
                        elif عمود == 5:  # عمود الرصيد
                            رصيد = float(قيمة) if قيمة else 0
                            عنصر = QTableWidgetItem(f"{رصيد:.2f}")
                            if رصيد > 0:
                                عنصر.setForeground(QColor("#e74c3c"))  # أحمر للدين
                            elif رصيد < 0:
                                عنصر.setForeground(QColor("#27ae60"))  # أخضر للرصيد الموجب
                        else:
                            عنصر = QTableWidgetItem(str(قيمة) if قيمة else "")

                        عنصر.setTextAlignment(Qt.AlignCenter)
                        self.جدول_الموردين.setItem(صف, عمود, عنصر)
            else:
                self.جدول_الموردين.setRowCount(0)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل الموردين: {str(e)}")

    def البحث_عن_الموردين(self):
        """
        دالة البحث عن الموردين
        """
        نص_البحث = self.حقل_البحث.text().strip()
        حالة_الفلتر = self.قائمة_حالة.currentText()

        try:
            شرط_البحث = "1=1"
            معاملات = []

            # شرط البحث النصي
            if نص_البحث:
                شرط_البحث += """ AND (
                    اسم_المورد LIKE %s OR العنوان LIKE %s OR
                    رقم_الهاتف LIKE %s OR البريد_الإلكتروني LIKE %s
                )"""
                معاملات.extend([f"%{نص_البحث}%"] * 4)

            # شرط فلتر الحالة
            if حالة_الفلتر != "الكل":
                شرط_البحث += " AND حالة_النشاط = %s"
                معاملات.append(حالة_الفلتر)

            استعلام = f"""
            SELECT رقم_المورد, اسم_المورد, العنوان, رقم_الهاتف,
                   البريد_الإلكتروني, الرصيد_الحالي, حالة_النشاط,
                   DATE_FORMAT(تاريخ_الإضافة, '%Y-%m-%d') as تاريخ_الإضافة
            FROM الموردين
            WHERE {شرط_البحث}
            ORDER BY تاريخ_الإضافة DESC
            """

            الموردين = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام, معاملات)

            self.جدول_الموردين.setRowCount(0)

            if الموردين:
                self.جدول_الموردين.setRowCount(len(الموردين))

                for صف, مورد in enumerate(الموردين):
                    for عمود, قيمة in enumerate(مورد):
                        if عمود == 6:  # عمود الحالة
                            حالة = "نشط" if قيمة == "نشط" else "غير نشط"
                            عنصر = QTableWidgetItem(حالة)
                            if حالة == "نشط":
                                عنصر.setForeground(QColor("#27ae60"))
                            else:
                                عنصر.setForeground(QColor("#e74c3c"))
                        elif عمود == 5:  # عمود الرصيد
                            رصيد = float(قيمة) if قيمة else 0
                            عنصر = QTableWidgetItem(f"{رصيد:.2f}")
                            if رصيد > 0:
                                عنصر.setForeground(QColor("#e74c3c"))
                            elif رصيد < 0:
                                عنصر.setForeground(QColor("#27ae60"))
                        else:
                            عنصر = QTableWidgetItem(str(قيمة) if قيمة else "")

                        عنصر.setTextAlignment(Qt.AlignCenter)
                        self.جدول_الموردين.setItem(صف, عمود, عنصر)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في البحث: {str(e)}")

    def تحديد_مورد(self):
        """
        دالة تحديد المورد المختار
        """
        صف_محدد = self.جدول_الموردين.currentRow()

        if صف_محدد >= 0:
            رقم_المورد = self.جدول_الموردين.item(صف_محدد, 0).text()
            self.المورد_المحدد = int(رقم_المورد)

            # تفعيل أزرار التعديل والحذف
            self.أزرار_الإجراءات["edit_supplier"].setEnabled(True)
            self.أزرار_الإجراءات["delete_supplier"].setEnabled(True)
            self.أزرار_الإجراءات["view_details"].setEnabled(True)
            self.أزرار_الإجراءات["supplier_report"].setEnabled(True)
        else:
            self.المورد_المحدد = None

            # تعطيل أزرار التعديل والحذف
            self.أزرار_الإجراءات["edit_supplier"].setEnabled(False)
            self.أزرار_الإجراءات["delete_supplier"].setEnabled(False)
            self.أزرار_الإجراءات["view_details"].setEnabled(False)
            self.أزرار_الإجراءات["supplier_report"].setEnabled(False)

    def عرض_قائمة_سياق(self, موضع):
        """
        دالة عرض قائمة السياق
        """
        if self.جدول_الموردين.itemAt(موضع):
            قائمة = QMenu(self)

            إجراء_تعديل = قائمة.addAction("تعديل المورد")
            إجراء_تعديل.triggered.connect(self.تعديل_مورد)

            إجراء_حذف = قائمة.addAction("حذف المورد")
            إجراء_حذف.triggered.connect(self.حذف_مورد)

            قائمة.addSeparator()

            إجراء_تفاصيل = قائمة.addAction("عرض التفاصيل")
            إجراء_تفاصيل.triggered.connect(self.عرض_تفاصيل_مورد)

            إجراء_تقرير = قائمة.addAction("تقرير المورد")
            إجراء_تقرير.triggered.connect(self.تقرير_مورد)

            قائمة.exec_(self.جدول_الموردين.mapToGlobal(موضع))

    # دوال الإجراءات
    def إضافة_مورد(self):
        """دالة إضافة مورد جديد"""
        QMessageBox.information(self, "معلومات", "وظيفة إضافة المورد قيد التطوير")

    def تعديل_مورد(self):
        """دالة تعديل المورد المحدد"""
        if not self.المورد_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد للتعديل")
            return
        QMessageBox.information(self, "معلومات", "وظيفة تعديل المورد قيد التطوير")

    def حذف_مورد(self):
        """دالة حذف المورد المحدد"""
        if not self.المورد_المحدد:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مورد للحذف")
            return

        # الحصول على اسم المورد
        صف_محدد = self.جدول_الموردين.currentRow()
        اسم_مورد = self.جدول_الموردين.item(صف_محدد, 1).text()

        رد = QMessageBox.question(self, "تأكيد الحذف",
                                  f"هل تريد حذف المورد '{اسم_مورد}'؟\n"
                                  "تحذير: سيتم حذف جميع البيانات المرتبطة بهذا المورد",
                                  QMessageBox.Yes | QMessageBox.No)

        if رد == QMessageBox.Yes:
            try:
                استعلام = "DELETE FROM الموردين WHERE رقم_المورد = %s"
                self.قاعدة_البيانات.تنفيذ_استعلام(استعلام, (self.المورد_المحدد,))

                QMessageBox.information(self, "نجح", "تم حذف المورد بنجاح")
                self.تحميل_الموردين()
                self.المورد_المحدد = None

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف المورد: {str(e)}")

    def عرض_تفاصيل_مورد(self):
        """دالة عرض تفاصيل المورد"""
        QMessageBox.information(self, "معلومات", "وظيفة تفاصيل المورد قيد التطوير")

    def تقرير_مورد(self):
        """دالة إنشاء تقرير المورد"""
        QMessageBox.information(self, "معلومات", "وظيفة تقرير المورد قيد التطوير")

    def طباعة_الموردين(self):
        """دالة طباعة قائمة الموردين"""
        QMessageBox.information(self, "معلومات", "وظيفة الطباعة قيد التطوير")

    def تصدير_الموردين(self):
        """دالة تصدير قائمة الموردين"""
        QMessageBox.information(self, "معلومات", "وظيفة التصدير قيد التطوير")

    def فلتر_متقدم(self):
        """دالة الفلتر المتقدم"""
        QMessageBox.information(self, "معلومات", "وظيفة الفلتر المتقدم قيد التطوير")
