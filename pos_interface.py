# -*- coding: utf-8 -*-
"""
واجهة نقطة البيع (Point of Sale)
تحتوي على البحث عن المنتجات وإضافتها للسلة وخيارات الدفع
"""

from PySide6.QtWidgets import *
from PySide6.QtCore import *
from PySide6.QtGui import *
from decimal import Decimal
from datetime import datetime
from db import قاعدة_البيانات
from customers_interface import واجهة_إدارة_العملاء

class واجهة_نقطة_البيع(QWidget):
    """
    واجهة نقطة البيع الرئيسية
    تحتوي على منطقة البحث والمنتجات والسلة والدفع
    """
    
    def __init__(self):
        """
        دالة التهيئة لواجهة نقطة البيع
        """
        super().__init__()
        self.قاعدة_البيانات = قاعدة_البيانات()
        self.قاعدة_البيانات.اتصال_قاعدة_البيانات()
        self.قاعدة_البيانات.cursor.execute(f"USE {self.قاعدة_البيانات.database}")
        
        self.سلة_المشتريات = []
        self.إجمالي_الفاتورة = Decimal('0.00')
        self.رقم_الفاتورة_الحالي = self.الحصول_على_رقم_فاتورة_جديد()
        
        self.إعداد_الواجهة()
        self.تطبيق_الأنماط()
    
    def إعداد_الواجهة(self):
        """
        دالة إعداد واجهة نقطة البيع
        """
        # التخطيط الرئيسي
        التخطيط_الرئيسي = QHBoxLayout(self)
        التخطيط_الرئيسي.setContentsMargins(10, 10, 10, 10)
        التخطيط_الرئيسي.setSpacing(15)
        
        # الجانب الأيمن - البحث والمنتجات
        self.إنشاء_منطقة_المنتجات(التخطيط_الرئيسي)
        
        # الجانب الأيسر - السلة والدفع
        self.إنشاء_منطقة_السلة(التخطيط_الرئيسي)
    
    def إنشاء_منطقة_المنتجات(self, التخطيط_الرئيسي):
        """
        دالة إنشاء منطقة البحث والمنتجات
        """
        # إطار المنتجات
        إطار_المنتجات = QFrame()
        إطار_المنتجات.setObjectName("products_frame")
        إطار_المنتجات.setMinimumWidth(500)
        
        تخطيط_المنتجات = QVBoxLayout(إطار_المنتجات)
        تخطيط_المنتجات.setContentsMargins(15, 15, 15, 15)
        تخطيط_المنتجات.setSpacing(10)
        
        # عنوان القسم
        عنوان_المنتجات = QLabel("البحث عن المنتجات")
        عنوان_المنتجات.setObjectName("section_title")
        تخطيط_المنتجات.addWidget(عنوان_المنتجات)
        
        # منطقة البحث
        self.إنشاء_منطقة_البحث(تخطيط_المنتجات)
        
        # منطقة إعدادات الكمية
        self.إنشاء_منطقة_الكمية(تخطيط_المنتجات)
        
        # جدول المنتجات
        self.إنشاء_جدول_المنتجات(تخطيط_المنتجات)
        
        التخطيط_الرئيسي.addWidget(إطار_المنتجات)
    
    def إنشاء_منطقة_البحث(self, تخطيط_المنتجات):
        """
        دالة إنشاء منطقة البحث
        """
        # إطار البحث
        إطار_البحث = QFrame()
        إطار_البحث.setObjectName("search_frame")
        إطار_البحث.setFixedHeight(80)
        
        تخطيط_البحث = QGridLayout(إطار_البحث)
        تخطيط_البحث.setContentsMargins(10, 10, 10, 10)
        تخطيط_البحث.setSpacing(10)
        
        # حقل البحث
        تسمية_البحث = QLabel("البحث:")
        self.حقل_البحث = QLineEdit()
        self.حقل_البحث.setPlaceholderText("اسم المنتج، الاسم التجاري، الفئة، أو الباركود...")
        self.حقل_البحث.textChanged.connect(self.البحث_عن_المنتجات)
        
        # نوع البحث
        تسمية_نوع_البحث = QLabel("نوع البحث:")
        self.قائمة_نوع_البحث = QComboBox()
        self.قائمة_نوع_البحث.addItems(["الكل", "الاسم العام", "الاسم التجاري", "الفئة", "الباركود"])
        self.قائمة_نوع_البحث.currentTextChanged.connect(self.البحث_عن_المنتجات)
        
        تخطيط_البحث.addWidget(تسمية_البحث, 0, 0)
        تخطيط_البحث.addWidget(self.حقل_البحث, 0, 1, 1, 2)
        تخطيط_البحث.addWidget(تسمية_نوع_البحث, 1, 0)
        تخطيط_البحث.addWidget(self.قائمة_نوع_البحث, 1, 1)
        
        تخطيط_المنتجات.addWidget(إطار_البحث)
    
    def إنشاء_منطقة_الكمية(self, تخطيط_المنتجات):
        """
        دالة إنشاء منطقة تحديد الكمية
        """
        # إطار الكمية
        إطار_الكمية = QFrame()
        إطار_الكمية.setObjectName("quantity_frame")
        إطار_الكمية.setFixedHeight(60)
        
        تخطيط_الكمية = QHBoxLayout(إطار_الكمية)
        تخطيط_الكمية.setContentsMargins(10, 10, 10, 10)
        تخطيط_الكمية.setSpacing(15)
        
        # تحديد الكمية
        تسمية_الكمية = QLabel("الكمية:")
        self.حقل_الكمية = QSpinBox()
        self.حقل_الكمية.setMinimum(1)
        self.حقل_الكمية.setMaximum(9999)
        self.حقل_الكمية.setValue(1)
        self.حقل_الكمية.setFixedWidth(80)
        
        تخطيط_الكمية.addWidget(تسمية_الكمية)
        تخطيط_الكمية.addWidget(self.حقل_الكمية)
        تخطيط_الكمية.addStretch()
        
        تخطيط_المنتجات.addWidget(إطار_الكمية)
    
    def إنشاء_جدول_المنتجات(self, تخطيط_المنتجات):
        """
        دالة إنشاء جدول عرض المنتجات
        """
        # جدول المنتجات
        self.جدول_المنتجات = QTableWidget()
        self.جدول_المنتجات.setObjectName("products_table")
        
        # إعداد الأعمدة
        الأعمدة = ["الباركود", "الاسم العام", "الاسم التجاري", "الفئة", "السعر", "الكمية المتوفرة", "الوحدة", "الصلاحية"]
        self.جدول_المنتجات.setColumnCount(len(الأعمدة))
        self.جدول_المنتجات.setHorizontalHeaderLabels(الأعمدة)
        
        # إعداد خصائص الجدول
        self.جدول_المنتجات.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.جدول_المنتجات.setAlternatingRowColors(True)
        self.جدول_المنتجات.setSortingEnabled(True)
        
        # تعديل عرض الأعمدة
        header = self.جدول_المنتجات.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # الاسم العام
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # الاسم التجاري
        
        # ربط النقر المزدوج بإضافة المنتج للسلة
        self.جدول_المنتجات.doubleClicked.connect(self.إضافة_منتج_للسلة)
        
        تخطيط_المنتجات.addWidget(self.جدول_المنتجات)
        
        # تحميل المنتجات
        self.تحميل_المنتجات()
    
    def إنشاء_منطقة_السلة(self, التخطيط_الرئيسي):
        """
        دالة إنشاء منطقة السلة والدفع
        """
        # إطار السلة
        إطار_السلة = QFrame()
        إطار_السلة.setObjectName("cart_frame")
        إطار_السلة.setFixedWidth(450)
        
        تخطيط_السلة = QVBoxLayout(إطار_السلة)
        تخطيط_السلة.setContentsMargins(15, 15, 15, 15)
        تخطيط_السلة.setSpacing(10)
        
        # معلومات الفاتورة
        self.إنشاء_معلومات_الفاتورة(تخطيط_السلة)
        
        # جدول السلة
        self.إنشاء_جدول_السلة(تخطيط_السلة)
        
        # معلومات الإجمالي
        self.إنشاء_معلومات_الإجمالي(تخطيط_السلة)
        
        # خيارات الدفع
        self.إنشاء_خيارات_الدفع(تخطيط_السلة)
        
        # أزرار الإجراءات
        self.إنشاء_أزرار_الإجراءات(تخطيط_السلة)
        
        التخطيط_الرئيسي.addWidget(إطار_السلة)
    
    def إنشاء_معلومات_الفاتورة(self, تخطيط_السلة):
        """
        دالة إنشاء معلومات الفاتورة
        """
        # إطار معلومات الفاتورة
        إطار_معلومات = QFrame()
        إطار_معلومات.setObjectName("invoice_info_frame")
        إطار_معلومات.setFixedHeight(80)
        
        تخطيط_معلومات = QGridLayout(إطار_معلومات)
        تخطيط_معلومات.setContentsMargins(10, 10, 10, 10)
        تخطيط_معلومات.setSpacing(10)
        
        # رقم الفاتورة
        تسمية_رقم_فاتورة = QLabel("رقم الفاتورة:")
        self.تسمية_رقم_فاتورة_قيمة = QLabel(str(self.رقم_الفاتورة_الحالي))
        self.تسمية_رقم_فاتورة_قيمة.setObjectName("invoice_number")
        
        # تاريخ الفاتورة
        تسمية_تاريخ = QLabel("التاريخ:")
        self.تسمية_تاريخ_قيمة = QLabel(datetime.now().strftime("%Y-%m-%d"))
        
        # زر البحث عن فاتورة
        زر_بحث_فاتورة = QPushButton("بحث عن فاتورة")
        زر_بحث_فاتورة.setObjectName("search_invoice_button")
        زر_بحث_فاتورة.clicked.connect(self.البحث_عن_فاتورة)
        
        تخطيط_معلومات.addWidget(تسمية_رقم_فاتورة, 0, 0)
        تخطيط_معلومات.addWidget(self.تسمية_رقم_فاتورة_قيمة, 0, 1)
        تخطيط_معلومات.addWidget(تسمية_تاريخ, 1, 0)
        تخطيط_معلومات.addWidget(self.تسمية_تاريخ_قيمة, 1, 1)
        تخطيط_معلومات.addWidget(زر_بحث_فاتورة, 0, 2, 2, 1)
        
        تخطيط_السلة.addWidget(إطار_معلومات)
    
    def إنشاء_جدول_السلة(self, تخطيط_السلة):
        """
        دالة إنشاء جدول السلة
        """
        # عنوان السلة
        عنوان_السلة = QLabel("سلة المشتريات")
        عنوان_السلة.setObjectName("section_title")
        تخطيط_السلة.addWidget(عنوان_السلة)
        
        # جدول السلة
        self.جدول_السلة = QTableWidget()
        self.جدول_السلة.setObjectName("cart_table")
        
        # إعداد الأعمدة
        أعمدة_السلة = ["المنتج", "الكمية", "السعر", "الإجمالي"]
        self.جدول_السلة.setColumnCount(len(أعمدة_السلة))
        self.جدول_السلة.setHorizontalHeaderLabels(أعمدة_السلة)
        
        # إعداد خصائص الجدول
        self.جدول_السلة.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.جدول_السلة.setAlternatingRowColors(True)
        
        # تعديل عرض الأعمدة
        header = self.جدول_السلة.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # المنتج
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # الكمية
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # السعر
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # الإجمالي
        
        # قائمة سياق للحذف
        self.جدول_السلة.setContextMenuPolicy(Qt.CustomContextMenu)
        self.جدول_السلة.customContextMenuRequested.connect(self.عرض_قائمة_سياق_السلة)
        
        تخطيط_السلة.addWidget(self.جدول_السلة)
    
    def إنشاء_معلومات_الإجمالي(self, تخطيط_السلة):
        """
        دالة إنشاء معلومات الإجمالي
        """
        # إطار الإجمالي
        إطار_الإجمالي = QFrame()
        إطار_الإجمالي.setObjectName("total_frame")
        إطار_الإجمالي.setFixedHeight(100)
        
        تخطيط_الإجمالي = QGridLayout(إطار_الإجمالي)
        تخطيط_الإجمالي.setContentsMargins(10, 10, 10, 10)
        تخطيط_الإجمالي.setSpacing(10)
        
        # الخصم
        تسمية_خصم = QLabel("الخصم:")
        self.حقل_خصم = QDoubleSpinBox()
        self.حقل_خصم.setMaximum(999999.99)
        self.حقل_خصم.setSuffix(" ريال")
        self.حقل_خصم.valueChanged.connect(self.حساب_الإجمالي)
        
        # الإجمالي
        تسمية_إجمالي = QLabel("الإجمالي:")
        self.تسمية_إجمالي_قيمة = QLabel("0.00 ريال")
        self.تسمية_إجمالي_قيمة.setObjectName("total_amount")
        
        تخطيط_الإجمالي.addWidget(تسمية_خصم, 0, 0)
        تخطيط_الإجمالي.addWidget(self.حقل_خصم, 0, 1)
        تخطيط_الإجمالي.addWidget(تسمية_إجمالي, 1, 0)
        تخطيط_الإجمالي.addWidget(self.تسمية_إجمالي_قيمة, 1, 1)
        
        تخطيط_السلة.addWidget(إطار_الإجمالي)

    def إنشاء_خيارات_الدفع(self, تخطيط_السلة):
        """
        دالة إنشاء خيارات الدفع
        """
        # إطار خيارات الدفع
        إطار_الدفع = QFrame()
        إطار_الدفع.setObjectName("payment_frame")
        إطار_الدفع.setFixedHeight(120)

        تخطيط_الدفع = QGridLayout(إطار_الدفع)
        تخطيط_الدفع.setContentsMargins(10, 10, 10, 10)
        تخطيط_الدفع.setSpacing(10)

        # نوع البيع
        تسمية_نوع_بيع = QLabel("نوع البيع:")
        self.قائمة_نوع_بيع = QComboBox()
        self.قائمة_نوع_بيع.addItems(["نقدي", "آجل", "أقساط"])
        self.قائمة_نوع_بيع.currentTextChanged.connect(self.تغيير_نوع_البيع)

        # العميل (يظهر عند اختيار آجل أو أقساط)
        self.تسمية_عميل = QLabel("العميل:")
        self.قائمة_عميل = QComboBox()
        self.قائمة_عميل.setEditable(True)
        self.زر_عميل_جديد = QPushButton("عميل جديد")
        self.زر_عميل_جديد.setObjectName("new_customer_button")
        self.زر_عميل_جديد.clicked.connect(واجهة_إدارة_العملاء.إضافة_عميل)

        # إخفاء خيارات العميل افتراضياً
        self.تسمية_عميل.hide()
        self.قائمة_عميل.hide()
        self.زر_عميل_جديد.hide()

        # المبلغ المدفوع
        تسمية_مدفوع = QLabel("المبلغ المدفوع:")
        self.حقل_مدفوع = QDoubleSpinBox()
        self.حقل_مدفوع.setMaximum(999999.99)
        self.حقل_مدفوع.setSuffix(" ريال")
        self.حقل_مدفوع.valueChanged.connect(self.حساب_الباقي)

        # الباقي
        تسمية_باقي = QLabel("الباقي:")
        self.تسمية_باقي_قيمة = QLabel("0.00 ريال")
        self.تسمية_باقي_قيمة.setObjectName("remaining_amount")

        تخطيط_الدفع.addWidget(تسمية_نوع_بيع, 0, 0)
        تخطيط_الدفع.addWidget(self.قائمة_نوع_بيع, 0, 1)
        تخطيط_الدفع.addWidget(self.تسمية_عميل, 1, 0)
        تخطيط_الدفع.addWidget(self.قائمة_عميل, 1, 1)
        تخطيط_الدفع.addWidget(self.زر_عميل_جديد, 1, 2)
        تخطيط_الدفع.addWidget(تسمية_مدفوع, 2, 0)
        تخطيط_الدفع.addWidget(self.حقل_مدفوع, 2, 1)
        تخطيط_الدفع.addWidget(تسمية_باقي, 3, 0)
        تخطيط_الدفع.addWidget(self.تسمية_باقي_قيمة, 3, 1)

        تخطيط_السلة.addWidget(إطار_الدفع)

        # تحميل العملاء
        self.تحميل_العملاء()

    def إنشاء_أزرار_الإجراءات(self, تخطيط_السلة):
        """
        دالة إنشاء أزرار الإجراءات
        """
        # إطار الأزرار
        إطار_أزرار = QFrame()
        إطار_أزرار.setFixedHeight(60)

        تخطيط_أزرار = QHBoxLayout(إطار_أزرار)
        تخطيط_أزرار.setContentsMargins(5, 5, 5, 5)
        تخطيط_أزرار.setSpacing(10)

        # زر إتمام البيع
        زر_إتمام = QPushButton("إتمام البيع")
        زر_إتمام.setObjectName("complete_sale_button")
        زر_إتمام.clicked.connect(self.إتمام_البيع)

        # زر مسح السلة
        زر_مسح = QPushButton("مسح السلة")
        زر_مسح.setObjectName("clear_cart_button")
        زر_مسح.clicked.connect(self.مسح_السلة)

        # زر طباعة
        زر_طباعة = QPushButton("طباعة")
        زر_طباعة.setObjectName("print_button")
        زر_طباعة.clicked.connect(self.طباعة_الفاتورة)

        تخطيط_أزرار.addWidget(زر_إتمام)
        تخطيط_أزرار.addWidget(زر_مسح)
        تخطيط_أزرار.addWidget(زر_طباعة)

        تخطيط_السلة.addWidget(إطار_أزرار)

    def تطبيق_الأنماط(self):
        """
        دالة تطبيق الأنماط CSS لواجهة نقطة البيع
        """
        نمط = """
        QFrame#products_frame, QFrame#cart_frame {
            background-color: white;
            border: 2px solid #bdc3c7;
            border-radius: 10px;
        }

        QFrame#search_frame, QFrame#quantity_frame, QFrame#invoice_info_frame,
        QFrame#total_frame, QFrame#payment_frame {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }

        QLabel#section_title {
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            padding: 5px;
            background-color: #ecf0f1;
            border-radius: 5px;
        }

        QLabel#invoice_number {
            font-size: 14px;
            font-weight: bold;
            color: #e74c3c;
        }

        QLabel#total_amount {
            font-size: 18px;
            font-weight: bold;
            color: #27ae60;
        }

        QLabel#remaining_amount {
            font-size: 14px;
            font-weight: bold;
            color: #f39c12;
        }

        QTableWidget#products_table, QTableWidget#cart_table {
            gridline-color: #bdc3c7;
            background-color: white;
            alternate-background-color: #f8f9fa;
            selection-background-color: #3498db;
        }

        QTableWidget#products_table::item, QTableWidget#cart_table::item {
            padding: 8px;
            text-align: center;
        }

        QHeaderView::section {
            background-color: #34495e;
            color: white;
            padding: 8px;
            border: none;
            font-weight: bold;
        }

        QPushButton#complete_sale_button {
            background-color: #27ae60;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px;
            font-size: 14px;
            font-weight: bold;
        }

        QPushButton#complete_sale_button:hover {
            background-color: #229954;
        }

        QPushButton#clear_cart_button {
            background-color: #e74c3c;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px;
            font-size: 14px;
            font-weight: bold;
        }

        QPushButton#clear_cart_button:hover {
            background-color: #c0392b;
        }

        QPushButton#print_button {
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px;
            font-size: 14px;
            font-weight: bold;
        }

        QPushButton#print_button:hover {
            background-color: #2980b9;
        }

        QPushButton#search_invoice_button, QPushButton#new_customer_button {
            background-color: #9b59b6;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 12px;
            font-weight: bold;
        }

        QPushButton#search_invoice_button:hover, QPushButton#new_customer_button:hover {
            background-color: #8e44ad;
        }

        QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox {
            border: 2px solid #bdc3c7;
            border-radius: 6px;
            padding: 8px;
            font-size: 12px;
        }

        QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus {
            border-color: #3498db;
        }
        """

        self.setStyleSheet(نمط)

    def الحصول_على_رقم_فاتورة_جديد(self):
        """
        دالة الحصول على رقم فاتورة جديد
        """
        try:
            استعلام = "SELECT MAX(رقم_الفاتورة) FROM فواتير_البيع"
            نتيجة = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام)
            if نتيجة and نتيجة[0][0]:
                return نتيجة[0][0] + 1
            return 1
        except:
            return 1

    def تحميل_المنتجات(self):
        """
        دالة تحميل جميع المنتجات
        """
        try:
            استعلام = """
            SELECT م.الباركود, م.الاسم_العام, م.الاسم_التجاري, ف.اسم_الفئة,
                   م.سعر_البيع, م.الكمية_الحالية, م.الوحدة, م.تاريخ_الصلاحية
            FROM المنتجات م
            LEFT JOIN الفئات ف ON م.رقم_الفئة = ف.رقم_الفئة
            WHERE م.حالة_النشاط = TRUE AND م.الكمية_الحالية > 0
            ORDER BY م.الاسم_العام
            """

            المنتجات = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام)

            if المنتجات:
                self.جدول_المنتجات.setRowCount(len(المنتجات))

                for صف, منتج in enumerate(المنتجات):
                    for عمود, قيمة in enumerate(منتج):
                        عنصر = QTableWidgetItem(str(قيمة) if قيمة else "")
                        عنصر.setTextAlignment(Qt.AlignCenter)
                        self.جدول_المنتجات.setItem(صف, عمود, عنصر)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل المنتجات: {str(e)}")

    def البحث_عن_المنتجات(self):
        """
        دالة البحث عن المنتجات
        """
        نص_البحث = self.حقل_البحث.text().strip()
        نوع_البحث = self.قائمة_نوع_البحث.currentText()

        if not نص_البحث:
            self.تحميل_المنتجات()
            return

        try:
            if نوع_البحث == "الكل":
                شرط_البحث = """
                (م.الاسم_العام LIKE %s OR م.الاسم_التجاري LIKE %s OR
                 م.الباركود LIKE %s OR ف.اسم_الفئة LIKE %s)
                """
                معاملات = [f"%{نص_البحث}%"] * 4
            elif نوع_البحث == "الاسم العام":
                شرط_البحث = "م.الاسم_العام LIKE %s"
                معاملات = [f"%{نص_البحث}%"]
            elif نوع_البحث == "الاسم التجاري":
                شرط_البحث = "م.الاسم_التجاري LIKE %s"
                معاملات = [f"%{نص_البحث}%"]
            elif نوع_البحث == "الفئة":
                شرط_البحث = "ف.اسم_الفئة LIKE %s"
                معاملات = [f"%{نص_البحث}%"]
            elif نوع_البحث == "الباركود":
                شرط_البحث = "م.الباركود LIKE %s"
                معاملات = [f"%{نص_البحث}%"]

            استعلام = f"""
            SELECT م.الباركود, م.الاسم_العام, م.الاسم_التجاري, ف.اسم_الفئة,
                   م.سعر_البيع, م.الكمية_الحالية, م.الوحدة, م.تاريخ_الصلاحية
            FROM المنتجات م
            LEFT JOIN الفئات ف ON م.رقم_الفئة = ف.رقم_الفئة
            WHERE م.حالة_النشاط = TRUE AND م.الكمية_الحالية > 0 AND {شرط_البحث}
            ORDER BY م.الاسم_العام
            """

            المنتجات = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام, معاملات)

            self.جدول_المنتجات.setRowCount(0)

            if المنتجات:
                self.جدول_المنتجات.setRowCount(len(المنتجات))

                for صف, منتج in enumerate(المنتجات):
                    for عمود, قيمة in enumerate(منتج):
                        عنصر = QTableWidgetItem(str(قيمة) if قيمة else "")
                        عنصر.setTextAlignment(Qt.AlignCenter)
                        self.جدول_المنتجات.setItem(صف, عمود, عنصر)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في البحث: {str(e)}")

    def تحميل_العملاء(self):
        """
        دالة تحميل قائمة العملاء
        """
        try:
            استعلام = """
            SELECT رقم_العميل, CONCAT(الاسم_الأول, ' ', اللقب) as الاسم_الكامل
            FROM العملاء
            WHERE حالة_النشاط = TRUE
            ORDER BY الاسم_الأول
            """

            العملاء = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام)

            self.قائمة_عميل.clear()
            self.قائمة_عميل.addItem("اختر العميل...", 0)

            if العملاء:
                for عميل in العملاء:
                    self.قائمة_عميل.addItem(عميل[1], عميل[0])

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل العملاء: {str(e)}")

    def إضافة_منتج_للسلة(self):
        """
        دالة إضافة منتج للسلة
        """
        صف_محدد = self.جدول_المنتجات.currentRow()

        if صف_محدد < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار منتج أولاً")
            return

        # الحصول على بيانات المنتج
        باركود = self.جدول_المنتجات.item(صف_محدد, 0).text()
        اسم_عام = self.جدول_المنتجات.item(صف_محدد, 1).text()
        اسم_تجاري = self.جدول_المنتجات.item(صف_محدد, 2).text()
        سعر = float(self.جدول_المنتجات.item(صف_محدد, 4).text())
        كمية_متوفرة = int(self.جدول_المنتجات.item(صف_محدد, 5).text())

        # الحصول على الكمية المطلوبة
        كمية_مطلوبة = self.حقل_الكمية.value()

        # التحقق من توفر الكمية
        if كمية_مطلوبة > كمية_متوفرة:
            QMessageBox.warning(self, "تحذير", f"الكمية المطلوبة ({كمية_مطلوبة}) أكبر من المتوفر ({كمية_متوفرة})")
            return

        # البحث عن المنتج في السلة
        منتج_موجود = False
        for i in range(self.جدول_السلة.rowCount()):
            if self.جدول_السلة.item(i, 0).data(Qt.UserRole) == باركود:
                # تحديث الكمية
                كمية_حالية = int(self.جدول_السلة.item(i, 1).text())
                كمية_جديدة = كمية_حالية + كمية_مطلوبة

                if كمية_جديدة > كمية_متوفرة:
                    QMessageBox.warning(self, "تحذير", f"إجمالي الكمية ({كمية_جديدة}) أكبر من المتوفر ({كمية_متوفرة})")
                    return

                self.جدول_السلة.item(i, 1).setText(str(كمية_جديدة))
                إجمالي_جديد = كمية_جديدة * سعر
                self.جدول_السلة.item(i, 3).setText(f"{إجمالي_جديد:.2f}")
                منتج_موجود = True
                break

        if not منتج_موجود:
            # إضافة منتج جديد للسلة
            صف_جديد = self.جدول_السلة.rowCount()
            self.جدول_السلة.insertRow(صف_جديد)

            # اسم المنتج
            عنصر_اسم = QTableWidgetItem(اسم_تجاري or اسم_عام)
            عنصر_اسم.setData(Qt.UserRole, باركود)
            عنصر_اسم.setTextAlignment(Qt.AlignCenter)
            self.جدول_السلة.setItem(صف_جديد, 0, عنصر_اسم)

            # الكمية
            عنصر_كمية = QTableWidgetItem(str(كمية_مطلوبة))
            عنصر_كمية.setTextAlignment(Qt.AlignCenter)
            self.جدول_السلة.setItem(صف_جديد, 1, عنصر_كمية)

            # السعر
            عنصر_سعر = QTableWidgetItem(f"{سعر:.2f}")
            عنصر_سعر.setTextAlignment(Qt.AlignCenter)
            self.جدول_السلة.setItem(صف_جديد, 2, عنصر_سعر)

            # الإجمالي
            إجمالي = كمية_مطلوبة * سعر
            عنصر_إجمالي = QTableWidgetItem(f"{إجمالي:.2f}")
            عنصر_إجمالي.setTextAlignment(Qt.AlignCenter)
            self.جدول_السلة.setItem(صف_جديد, 3, عنصر_إجمالي)

        # إعادة حساب الإجمالي
        self.حساب_الإجمالي()

        # إعادة تعيين الكمية إلى 1
        self.حقل_الكمية.setValue(1)

    def حساب_الإجمالي(self):
        """
        دالة حساب إجمالي الفاتورة
        """
        إجمالي = Decimal('0.00')

        for i in range(self.جدول_السلة.rowCount()):
            إجمالي_صف = Decimal(self.جدول_السلة.item(i, 3).text())
            إجمالي += إجمالي_صف

        # طرح الخصم
        خصم = Decimal(str(self.حقل_خصم.value()))
        إجمالي_نهائي = إجمالي - خصم

        self.إجمالي_الفاتورة = إجمالي_نهائي
        self.تسمية_إجمالي_قيمة.setText(f"{إجمالي_نهائي:.2f} ريال")

        # حساب الباقي
        self.حساب_الباقي()

    def حساب_الباقي(self):
        """
        دالة حساب المبلغ الباقي
        """
        مدفوع = Decimal(str(self.حقل_مدفوع.value()))
        باقي = self.إجمالي_الفاتورة - مدفوع

        self.تسمية_باقي_قيمة.setText(f"{باقي:.2f} ريال")

        # تغيير لون النص حسب الحالة
        if باقي > 0:
            self.تسمية_باقي_قيمة.setStyleSheet("color: #e74c3c; font-weight: bold;")
        elif باقي < 0:
            self.تسمية_باقي_قيمة.setStyleSheet("color: #f39c12; font-weight: bold;")
        else:
            self.تسمية_باقي_قيمة.setStyleSheet("color: #27ae60; font-weight: bold;")

    def تغيير_نوع_البيع(self):
        """
        دالة تغيير نوع البيع وإظهار/إخفاء خيارات العميل
        """
        نوع_البيع = self.قائمة_نوع_بيع.currentText()

        if نوع_البيع in ["آجل", "أقساط"]:
            self.تسمية_عميل.show()
            self.قائمة_عميل.show()
            self.زر_عميل_جديد.show()
        else:
            self.تسمية_عميل.hide()
            self.قائمة_عميل.hide()
            self.زر_عميل_جديد.hide()

    def عرض_قائمة_سياق_السلة(self, موضع):
        """
        دالة عرض قائمة السياق لحذف عنصر من السلة
        """
        if self.جدول_السلة.itemAt(موضع):
            قائمة = QMenu(self)
            إجراء_حذف = قائمة.addAction("حذف من السلة")
            إجراء_حذف.triggered.connect(self.حذف_من_السلة)
            قائمة.exec_(self.جدول_السلة.mapToGlobal(موضع))

    def حذف_من_السلة(self):
        """
        دالة حذف عنصر من السلة
        """
        صف_محدد = self.جدول_السلة.currentRow()
        if صف_محدد >= 0:
            self.جدول_السلة.removeRow(صف_محدد)
            self.حساب_الإجمالي()

    def مسح_السلة(self):
        """
        دالة مسح جميع عناصر السلة
        """
        رد = QMessageBox.question(self, "تأكيد", "هل تريد مسح جميع عناصر السلة؟",
                                  QMessageBox.Yes | QMessageBox.No)

        if رد == QMessageBox.Yes:
            self.جدول_السلة.setRowCount(0)
            self.حقل_خصم.setValue(0)
            self.حقل_مدفوع.setValue(0)
            self.حساب_الإجمالي()

    def البحث_عن_فاتورة(self):
        """
        دالة البحث عن فاتورة سابقة
        """
        رقم_فاتورة, موافق = QInputDialog.getInt(self, "البحث عن فاتورة",
                                                  "أدخل رقم الفاتورة:",
                                                  1, 1, 999999)

        if موافق:
            try:
                # البحث عن الفاتورة
                استعلام_فاتورة = """
                SELECT رقم_العميل, نوع_البيع, إجمالي_الفاتورة, الخصم,
                       المبلغ_المدفوع, تاريخ_الفاتورة
                FROM فواتير_البيع
                WHERE رقم_الفاتورة = %s
                """

                فاتورة = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام_فاتورة, (رقم_فاتورة,))

                if not فاتورة:
                    QMessageBox.warning(self, "تحذير", "لم يتم العثور على الفاتورة")
                    return

                # تحميل تفاصيل الفاتورة
                استعلام_تفاصيل = """
                SELECT م.الاسم_العام, م.الاسم_التجاري, ت.الكمية, ت.سعر_البيع, ت.الإجمالي
                FROM تفاصيل_فواتير_البيع ت
                JOIN المنتجات م ON ت.رقم_المنتج = م.رقم_المنتج
                WHERE ت.رقم_الفاتورة = %s
                """

                تفاصيل = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام_تفاصيل, (رقم_فاتورة,))

                # مسح السلة الحالية
                self.جدول_السلة.setRowCount(0)

                # تحميل التفاصيل في السلة
                if تفاصيل:
                    for i, تفصيل in enumerate(تفاصيل):
                        self.جدول_السلة.insertRow(i)

                        # اسم المنتج
                        اسم_منتج = تفصيل[1] or تفصيل[0]
                        عنصر_اسم = QTableWidgetItem(اسم_منتج)
                        عنصر_اسم.setTextAlignment(Qt.AlignCenter)
                        self.جدول_السلة.setItem(i, 0, عنصر_اسم)

                        # الكمية
                        عنصر_كمية = QTableWidgetItem(str(تفصيل[2]))
                        عنصر_كمية.setTextAlignment(Qt.AlignCenter)
                        self.جدول_السلة.setItem(i, 1, عنصر_كمية)

                        # السعر
                        عنصر_سعر = QTableWidgetItem(f"{تفصيل[3]:.2f}")
                        عنصر_سعر.setTextAlignment(Qt.AlignCenter)
                        self.جدول_السلة.setItem(i, 2, عنصر_سعر)

                        # الإجمالي
                        عنصر_إجمالي = QTableWidgetItem(f"{تفصيل[4]:.2f}")
                        عنصر_إجمالي.setTextAlignment(Qt.AlignCenter)
                        self.جدول_السلة.setItem(i, 3, عنصر_إجمالي)

                # تحديث معلومات الفاتورة
                بيانات_فاتورة = فاتورة[0]
                self.قائمة_نوع_بيع.setCurrentText(بيانات_فاتورة[1])
                self.حقل_خصم.setValue(float(بيانات_فاتورة[3]))
                self.حقل_مدفوع.setValue(float(بيانات_فاتورة[4]))

                # تحديث رقم الفاتورة
                self.رقم_الفاتورة_الحالي = رقم_فاتورة
                self.تسمية_رقم_فاتورة_قيمة.setText(str(رقم_فاتورة))

                # إعادة حساب الإجمالي
                self.حساب_الإجمالي()

                QMessageBox.information(self, "نجح", "تم تحميل الفاتورة بنجاح")

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في تحميل الفاتورة: {str(e)}")

    def إتمام_البيع(self):
        """
        دالة إتمام عملية البيع وحفظ الفاتورة
        """
        # التحقق من وجود منتجات في السلة
        if self.جدول_السلة.rowCount() == 0:
            QMessageBox.warning(self, "تحذير", "لا توجد منتجات في السلة")
            return

        # التحقق من اختيار العميل في حالة البيع الآجل أو الأقساط
        نوع_البيع = self.قائمة_نوع_بيع.currentText()
        رقم_العميل = None

        if نوع_البيع in ["آجل", "أقساط"]:
            رقم_العميل = self.قائمة_عميل.currentData()
            if not رقم_العميل or رقم_العميل == 0:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار العميل")
                return

        try:
            # حفظ الفاتورة
            استعلام_فاتورة = """
            INSERT INTO فواتير_البيع
            (رقم_العميل, نوع_البيع, إجمالي_الفاتورة, الخصم, المبلغ_النهائي,
             المبلغ_المدفوع, المبلغ_المتبقي, تاريخ_الفاتورة, حالة_السداد)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """

            إجمالي_قبل_خصم = self.إجمالي_الفاتورة + Decimal(str(self.حقل_خصم.value()))
            خصم = Decimal(str(self.حقل_خصم.value()))
            مدفوع = Decimal(str(self.حقل_مدفوع.value()))
            متبقي = self.إجمالي_الفاتورة - مدفوع

            # تحديد حالة السداد
            if متبقي <= 0:
                حالة_سداد = "مسددة"
            elif مدفوع > 0:
                حالة_سداد = "جزئية"
            else:
                حالة_سداد = "غير مسددة"

            معاملات_فاتورة = (
                رقم_العميل, نوع_البيع, float(إجمالي_قبل_خصم), float(خصم),
                float(self.إجمالي_الفاتورة), float(مدفوع), float(متبقي),
                datetime.now().date(), حالة_سداد
            )

            self.قاعدة_البيانات.تنفيذ_استعلام(استعلام_فاتورة, معاملات_فاتورة)

            # الحصول على رقم الفاتورة المحفوظة
            رقم_فاتورة = self.قاعدة_البيانات.cursor.lastrowid

            # حفظ تفاصيل الفاتورة وتحديث المخزون
            for i in range(self.جدول_السلة.rowCount()):
                باركود = self.جدول_السلة.item(i, 0).data(Qt.UserRole)
                كمية = int(self.جدول_السلة.item(i, 1).text())
                سعر = float(self.جدول_السلة.item(i, 2).text())
                إجمالي = float(self.جدول_السلة.item(i, 3).text())

                # الحصول على رقم المنتج
                استعلام_منتج = "SELECT رقم_المنتج FROM المنتجات WHERE الباركود = %s"
                منتج = self.قاعدة_البيانات.تنفيذ_استعلام(استعلام_منتج, (باركود,))

                if منتج:
                    رقم_المنتج = منتج[0][0]

                    # حفظ تفاصيل الفاتورة
                    استعلام_تفاصيل = """
                    INSERT INTO تفاصيل_فواتير_البيع
                    (رقم_الفاتورة, رقم_المنتج, الكمية, سعر_البيع, الإجمالي)
                    VALUES (%s, %s, %s, %s, %s)
                    """

                    معاملات_تفاصيل = (رقم_فاتورة, رقم_المنتج, كمية, سعر, إجمالي)
                    self.قاعدة_البيانات.تنفيذ_استعلام(استعلام_تفاصيل, معاملات_تفاصيل)

                    # تحديث كمية المنتج في المخزون
                    استعلام_تحديث = """
                    UPDATE المنتجات
                    SET الكمية_الحالية = الكمية_الحالية - %s
                    WHERE رقم_المنتج = %s
                    """

                    self.قاعدة_البيانات.تنفيذ_استعلام(استعلام_تحديث, (كمية, رقم_المنتج))

            QMessageBox.information(self, "نجح", f"تم حفظ الفاتورة رقم {رقم_فاتورة} بنجاح")

            # مسح السلة وإعداد فاتورة جديدة
            self.مسح_السلة()
            self.رقم_الفاتورة_الحالي = self.الحصول_على_رقم_فاتورة_جديد()
            self.تسمية_رقم_فاتورة_قيمة.setText(str(self.رقم_الفاتورة_الحالي))
            self.تسمية_تاريخ_قيمة.setText(datetime.now().strftime("%Y-%m-%d"))

            # تحديث جدول المنتجات
            self.تحميل_المنتجات()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الفاتورة: {str(e)}")

    def طباعة_الفاتورة(self):
        """
        دالة طباعة الفاتورة
        """
        if self.جدول_السلة.rowCount() == 0:
            QMessageBox.warning(self, "تحذير", "لا توجد منتجات للطباعة")
            return

        # سيتم تطوير وظيفة الطباعة لاحقاً
        QMessageBox.information(self, "معلومات", "وظيفة الطباعة قيد التطوير")
