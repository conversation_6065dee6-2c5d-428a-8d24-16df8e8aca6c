# -*- coding: utf-8 -*-
"""
اختبار الواجهات الجديدة المطورة
يتضمن اختبار واجهة الأقساط والديون، الموردين، والعقود
"""

import sys
from PySide6.QtWidgets import QApplication, QMainWindow, QTabWidget
from PySide6.QtCore import Qt
from db import تهيئة_قاعدة_البيانات
from other_interfaces import واجهة_الأقساط, واجهة_العقود
from suppliers_interface import واجهة_إدارة_الموردين

class نافذة_اختبار_الواجهات_الجديدة(QMainWindow):
    """
    نافذة اختبار الواجهات الجديدة
    """
    
    def __init__(self):
        """
        دالة التهيئة لنافذة الاختبار
        """
        super().__init__()
        self.إعداد_النافذة()
        self.إنشاء_التبويبات()
    
    def إعداد_النافذة(self):
        """
        دالة إعداد النافذة الرئيسية
        """
        self.setWindowTitle("اختبار الواجهات الجديدة - نظام إدارة المبيعات")
        self.setGeometry(100, 100, 1400, 800)
        self.setLayoutDirection(Qt.RightToLeft)
    
    def إنشاء_التبويبات(self):
        """
        دالة إنشاء التبويبات للواجهات المختلفة
        """
        # إنشاء التبويبات الرئيسية
        التبويبات = QTabWidget()
        التبويبات.setTabPosition(QTabWidget.North)
        
        # تبويب واجهة الأقساط والديون
        try:
            واجهة_أقساط = واجهة_الأقساط()
            التبويبات.addTab(واجهة_أقساط, "إدارة الأقساط والديون")
        except Exception as e:
            print(f"خطأ في تحميل واجهة الأقساط: {str(e)}")
        
        # تبويب واجهة الموردين
        try:
            واجهة_موردين = واجهة_إدارة_الموردين()
            التبويبات.addTab(واجهة_موردين, "إدارة الموردين")
        except Exception as e:
            print(f"خطأ في تحميل واجهة الموردين: {str(e)}")
        
        # تبويب واجهة العقود
        try:
            واجهة_عقود = واجهة_العقود()
            التبويبات.addTab(واجهة_عقود, "إدارة العقود")
        except Exception as e:
            print(f"خطأ في تحميل واجهة العقود: {str(e)}")
        
        # تطبيق الأنماط
        التبويبات.setStyleSheet("""
        QTabWidget::pane {
            border: 1px solid #bdc3c7;
            border-radius: 8px;
            background-color: white;
        }
        
        QTabBar::tab {
            background-color: #ecf0f1;
            color: #2c3e50;
            padding: 10px 20px;
            margin-right: 2px;
            border-top-left-radius: 6px;
            border-top-right-radius: 6px;
            font-weight: bold;
            font-size: 12px;
        }
        
        QTabBar::tab:selected {
            background-color: #3498db;
            color: white;
        }
        
        QTabBar::tab:hover {
            background-color: #5dade2;
            color: white;
        }
        """)
        
        self.setCentralWidget(التبويبات)

def main():
    """
    دالة تشغيل التطبيق الرئيسية
    """
    # إنشاء التطبيق
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تهيئة قاعدة البيانات
    print("جاري تهيئة قاعدة البيانات...")
    try:
        تهيئة_قاعدة_البيانات()
        print("تم تهيئة قاعدة البيانات بنجاح")
    except Exception as e:
        print(f"خطأ في تهيئة قاعدة البيانات: {str(e)}")
        return
    
    # إنشاء النافذة الرئيسية
    نافذة = نافذة_اختبار_الواجهات_الجديدة()
    نافذة.show()
    
    # تشغيل التطبيق
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
